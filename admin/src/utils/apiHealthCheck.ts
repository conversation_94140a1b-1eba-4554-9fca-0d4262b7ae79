import axiosInstance from '../api/axiosInstance';

export interface HealthCheckResult {
  endpoint: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  responseTime?: number;
  data?: any;
}

export interface HealthCheckReport {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  results: HealthCheckResult[];
  timestamp: string;
}

// API端点健康检查配置
const API_ENDPOINTS = [
  {
    name: '用户信息',
    endpoint: '/auth/users/me',
    method: 'GET',
    requireAuth: true,
  },
  {
    name: '统计数据',
    endpoint: '/stats/summary',
    method: 'GET',
    requireAuth: true,
  },
  {
    name: '文章列表',
    endpoint: '/blogs/',
    method: 'GET',
    requireAuth: true,
    params: { limit: 1 }
  },
  {
    name: '标签列表',
    endpoint: '/tags/',
    method: 'GET',
    requireAuth: true,
  },
  {
    name: '个人信息配置',
    endpoint: '/site-settings/personal-info/config',
    method: 'GET',
    requireAuth: true,
  },
  {
    name: '主题颜色配置',
    endpoint: '/site-settings/theme-colors/config',
    method: 'GET',
    requireAuth: true,
  },
  {
    name: '图片搜索',
    endpoint: '/images/search',
    method: 'POST',
    requireAuth: true,
    data: { page: 1, page_size: 1 }
  },
];

// 执行单个API端点检查
async function checkEndpoint(config: typeof API_ENDPOINTS[0]): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    let response;
    
    switch (config.method) {
      case 'GET':
        response = await axiosInstance.get(config.endpoint, { 
          params: config.params,
          timeout: 5000 
        });
        break;
      case 'POST':
        response = await axiosInstance.post(config.endpoint, config.data, { 
          timeout: 5000 
        });
        break;
      default:
        throw new Error(`Unsupported method: ${config.method}`);
    }
    
    const responseTime = Date.now() - startTime;
    
    return {
      endpoint: `${config.method} ${config.endpoint}`,
      status: 'success',
      message: `${config.name} - 响应正常`,
      responseTime,
      data: response.data
    };
    
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    
    if (error.code === 'ECONNABORTED') {
      return {
        endpoint: `${config.method} ${config.endpoint}`,
        status: 'error',
        message: `${config.name} - 请求超时`,
        responseTime,
      };
    }
    
    if (error.response) {
      const status = error.response.status;
      let statusType: 'error' | 'warning' = 'error';
      let message = `${config.name} - HTTP ${status}`;
      
      if (status === 401) {
        statusType = 'warning';
        message += ' (未授权，可能需要重新登录)';
      } else if (status === 403) {
        statusType = 'warning';
        message += ' (权限不足)';
      } else if (status === 404) {
        statusType = 'error';
        message += ' (端点不存在)';
      } else if (status >= 500) {
        statusType = 'error';
        message += ' (服务器错误)';
      }
      
      return {
        endpoint: `${config.method} ${config.endpoint}`,
        status: statusType,
        message,
        responseTime,
        data: error.response.data
      };
    }
    
    return {
      endpoint: `${config.method} ${config.endpoint}`,
      status: 'error',
      message: `${config.name} - 网络错误: ${error.message}`,
      responseTime,
    };
  }
}

// 执行完整的健康检查
export async function performHealthCheck(): Promise<HealthCheckReport> {
  // console.log('🔍 开始API健康检查...'); // 已清理调试代码
  
  const results: HealthCheckResult[] = [];
  
  // 并发执行所有检查
  const promises = API_ENDPOINTS.map(config => checkEndpoint(config));
  const checkResults = await Promise.allSettled(promises);
  
  checkResults.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      results.push(result.value);
    } else {
      results.push({
        endpoint: API_ENDPOINTS[index].endpoint,
        status: 'error',
        message: `${API_ENDPOINTS[index].name} - 检查失败: ${result.reason?.message || '未知错误'}`,
      });
    }
  });
  
  // 计算整体健康状态
  const errorCount = results.filter(r => r.status === 'error').length;
  const warningCount = results.filter(r => r.status === 'warning').length;
  
  let overall: 'healthy' | 'degraded' | 'unhealthy';
  if (errorCount === 0 && warningCount === 0) {
    overall = 'healthy';
  } else if (errorCount === 0 && warningCount > 0) {
    overall = 'degraded';
  } else {
    overall = 'unhealthy';
  }
  
  const report: HealthCheckReport = {
    overall,
    results,
    timestamp: new Date().toISOString(),
  };
  
  // console.log('🔍 API健康检查完成:', report); // 已清理调试代码
  return report;
}

// 快速检查关键API
export async function quickHealthCheck(): Promise<boolean> {
  try {
    // 只检查最关键的几个端点
    const criticalEndpoints = [
      '/auth/users/me',
      '/stats/summary',
    ];
    
    const promises = criticalEndpoints.map(endpoint => 
      axiosInstance.get(endpoint, { timeout: 3000 })
    );
    
    await Promise.all(promises);
    return true;
  } catch (error) {
    console.error('快速健康检查失败:', error);
    return false;
  }
}
