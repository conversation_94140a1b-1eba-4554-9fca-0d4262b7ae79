/**
 * 统一日志管理系统
 * 在开发环境输出到控制台，生产环境可以发送到远程日志服务
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

interface LogEntry {
  level: LogLevel;
  message: string;
  data?: any;
  timestamp: number;
  source?: string;
}

class Logger {
  private isDevelopment = import.meta.env.DEV;
  private minLevel = this.isDevelopment ? LogLevel.DEBUG : LogLevel.WARN;
  private logs: LogEntry[] = [];
  private maxLogs = 1000;

  private shouldLog(level: LogLevel): boolean {
    return level >= this.minLevel;
  }

  private formatMessage(level: LogLevel, message: string, source?: string): string {
    const timestamp = new Date().toISOString();
    const levelStr = LogLevel[level];
    const sourceStr = source ? `[${source}]` : '';
    return `${timestamp} ${levelStr} ${sourceStr} ${message}`;
  }

  private addLog(level: LogLevel, message: string, data?: any, source?: string): void {
    const entry: LogEntry = {
      level,
      message,
      data,
      timestamp: Date.now(),
      source,
    };

    this.logs.push(entry);
    
    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
  }

  debug(message: string, data?: any, source?: string): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;
    
    this.addLog(LogLevel.DEBUG, message, data, source);
    
    if (this.isDevelopment) {
      const formattedMessage = this.formatMessage(LogLevel.DEBUG, message, source);
      if (data) {
        // console.debug(formattedMessage, data); // 已清理调试代码
      } else {
        // console.debug(formattedMessage); // 已清理调试代码
      }
    }
  }

  info(message: string, data?: any, source?: string): void {
    if (!this.shouldLog(LogLevel.INFO)) return;
    
    this.addLog(LogLevel.INFO, message, data, source);
    
    if (this.isDevelopment) {
      const formattedMessage = this.formatMessage(LogLevel.INFO, message, source);
      if (data) {
        // console.info(formattedMessage, data); // 已清理调试代码
      } else {
        // console.info(formattedMessage); // 已清理调试代码
      }
    }
  }

  warn(message: string, data?: any, source?: string): void {
    if (!this.shouldLog(LogLevel.WARN)) return;
    
    this.addLog(LogLevel.WARN, message, data, source);
    
    const formattedMessage = this.formatMessage(LogLevel.WARN, message, source);
    if (data) {
      console.warn(formattedMessage, data);
    } else {
      console.warn(formattedMessage);
    }
  }

  error(message: string, data?: any, source?: string): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;
    
    this.addLog(LogLevel.ERROR, message, data, source);
    
    const formattedMessage = this.formatMessage(LogLevel.ERROR, message, source);
    if (data) {
      console.error(formattedMessage, data);
    } else {
      console.error(formattedMessage);
    }
  }

  // API调用专用日志
  api(method: string, url: string, data?: any, response?: any): void {
    if (this.isDevelopment) {
      this.debug(`API ${method} ${url}`, { data, response }, 'API');
    }
  }

  // 性能监控专用日志
  performance(operation: string, duration: number, data?: any): void {
    if (this.isDevelopment) {
      this.debug(`Performance: ${operation} took ${duration}ms`, data, 'PERF');
    }
  }

  // 获取所有日志
  getLogs(): LogEntry[] {
    return [...this.logs];
  }

  // 清除日志
  clearLogs(): void {
    this.logs = [];
  }

  // 导出日志
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }
}

// 创建全局日志实例
export const logger = new Logger();

// 便捷方法
export const log = {
  debug: (message: string, data?: any, source?: string) => logger.debug(message, data, source),
  info: (message: string, data?: any, source?: string) => logger.info(message, data, source),
  warn: (message: string, data?: any, source?: string) => logger.warn(message, data, source),
  error: (message: string, data?: any, source?: string) => logger.error(message, data, source),
  api: (method: string, url: string, data?: any, response?: any) => logger.api(method, url, data, response),
  performance: (operation: string, duration: number, data?: any) => logger.performance(operation, duration, data),

  // 页面性能监控
  pageLoad: (pageName: string, loadTime: number, data?: any) => {
    logger.info(`[PAGE] ${pageName} loaded in ${loadTime}ms`, { loadTime, ...data }, 'PAGE_PERFORMANCE');
  },

  // 组件渲染性能
  componentRender: (componentName: string, renderTime: number, data?: any) => {
    logger.debug(`[COMPONENT] ${componentName} rendered in ${renderTime}ms`, { renderTime, ...data }, 'COMPONENT_PERFORMANCE');
  },

  // API请求性能
  apiPerformance: (url: string, method: string, duration: number, data?: any) => {
    logger.info(`[API] ${method} ${url} completed in ${duration}ms`, { duration, method, url, ...data }, 'API_PERFORMANCE');
  }
};

export default logger;
