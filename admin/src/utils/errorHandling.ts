// 错误处理和日志系统

interface ErrorInfo {
  message: string;
  stack?: string;
  timestamp: number;
  url: string;
  userAgent: string;
  userId?: string;
  sessionId: string;
  level: 'error' | 'warn' | 'info' | 'debug';
  category: string;
  tags?: string[];
  context?: Record<string, any>;
}

interface LoggerConfig {
  enableConsole: boolean;
  enableRemote: boolean;
  enableStorage: boolean;
  maxStorageSize: number;
  remoteEndpoint?: string;
  logLevel: 'error' | 'warn' | 'info' | 'debug';
  enableStackTrace: boolean;
  enableUserTracking: boolean;
}

class ErrorLogger {
  private config: LoggerConfig;
  private sessionId: string;
  private errorQueue: ErrorInfo[] = [];
  private isOnline = navigator.onLine;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      enableConsole: process.env.NODE_ENV === 'development',
      enableRemote: process.env.NODE_ENV === 'production',
      enableStorage: true,
      maxStorageSize: 100,
      logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'error',
      enableStackTrace: true,
      enableUserTracking: false,
      ...config
    };

    this.sessionId = this.generateSessionId();
    this.setupGlobalErrorHandlers();
    this.setupNetworkListeners();
  }

  // 生成会话ID
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 设置全局错误处理器
  private setupGlobalErrorHandlers(): void {
    // 捕获JavaScript错误
    window.addEventListener('error', (event) => {
      this.logError({
        message: event.message,
        stack: event.error?.stack,
        category: 'javascript',
        context: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      });
    });

    // 捕获Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        category: 'promise',
        context: {
          reason: event.reason
        }
      });
    });

    // 捕获资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.logError({
          message: `Resource loading error: ${(event.target as any)?.src || (event.target as any)?.href}`,
          category: 'resource',
          context: {
            tagName: (event.target as any)?.tagName,
            src: (event.target as any)?.src,
            href: (event.target as any)?.href
          }
        });
      }
    }, true);
  }

  // 设置网络状态监听
  private setupNetworkListeners(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushErrorQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  // 记录错误
  logError(error: Partial<ErrorInfo> & { message: string }): void {
    const errorInfo: ErrorInfo = {
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      sessionId: this.sessionId,
      level: 'error',
      category: 'unknown',
      ...error
    };

    this.processError(errorInfo);
  }

  // 记录警告
  logWarning(message: string, context?: Record<string, any>): void {
    if (this.shouldLog('warn')) {
      this.logError({
        message,
        level: 'warn',
        category: 'warning',
        context
      });
    }
  }

  // 记录信息
  logInfo(message: string, context?: Record<string, any>): void {
    if (this.shouldLog('info')) {
      this.logError({
        message,
        level: 'info',
        category: 'info',
        context
      });
    }
  }

  // 记录调试信息
  logDebug(message: string, context?: Record<string, any>): void {
    if (this.shouldLog('debug')) {
      this.logError({
        message,
        level: 'debug',
        category: 'debug',
        context
      });
    }
  }

  // 处理错误
  private processError(errorInfo: ErrorInfo): void {
    // 控制台输出
    if (this.config.enableConsole) {
      this.logToConsole(errorInfo);
    }

    // 本地存储
    if (this.config.enableStorage) {
      this.saveToStorage(errorInfo);
    }

    // 远程上报
    if (this.config.enableRemote) {
      if (this.isOnline) {
        this.sendToRemote(errorInfo);
      } else {
        this.errorQueue.push(errorInfo);
      }
    }
  }

  // 控制台输出
  private logToConsole(errorInfo: ErrorInfo): void {
    const { level, message, stack, context } = errorInfo;
    // const logMethod = console[level] || console.log; // 已清理调试代码
    
    logMethod.call(console, `[${level.toUpperCase()}] ${message}`, {
      timestamp: new Date(errorInfo.timestamp).toISOString(),
      stack,
      context,
      sessionId: this.sessionId
    });
  }

  // 保存到本地存储
  private saveToStorage(errorInfo: ErrorInfo): void {
    try {
      const storageKey = 'admin-error-logs';
      const stored = localStorage.getItem(storageKey);
      const logs: ErrorInfo[] = stored ? JSON.parse(stored) : [];
      
      logs.push(errorInfo);
      
      // 限制存储大小
      if (logs.length > this.config.maxStorageSize) {
        logs.splice(0, logs.length - this.config.maxStorageSize);
      }
      
      localStorage.setItem(storageKey, JSON.stringify(logs));
    } catch (error) {
      console.warn('Failed to save error to storage:', error);
    }
  }

  // 发送到远程服务器
  private async sendToRemote(errorInfo: ErrorInfo): Promise<void> {
    if (!this.config.remoteEndpoint) return;

    try {
      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorInfo)
      });
    } catch (error) {
      // 发送失败，加入队列等待重试
      this.errorQueue.push(errorInfo);
    }
  }

  // 刷新错误队列
  private async flushErrorQueue(): Promise<void> {
    if (this.errorQueue.length === 0) return;

    const errors = [...this.errorQueue];
    this.errorQueue = [];

    for (const error of errors) {
      try {
        await this.sendToRemote(error);
      } catch {
        // 重新加入队列
        this.errorQueue.push(error);
      }
    }
  }

  // 检查是否应该记录日志
  private shouldLog(level: ErrorInfo['level']): boolean {
    const levels = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(this.config.logLevel);
    const messageLevelIndex = levels.indexOf(level);
    
    return messageLevelIndex >= currentLevelIndex;
  }

  // 获取存储的错误日志
  getStoredLogs(): ErrorInfo[] {
    try {
      const stored = localStorage.getItem('admin-error-logs');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  // 清除存储的错误日志
  clearStoredLogs(): void {
    localStorage.removeItem('admin-error-logs');
  }

  // 获取错误统计
  getErrorStats(): {
    totalErrors: number;
    errorsByCategory: Record<string, number>;
    errorsByLevel: Record<string, number>;
    recentErrors: ErrorInfo[];
  } {
    const logs = this.getStoredLogs();
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;

    const errorsByCategory: Record<string, number> = {};
    const errorsByLevel: Record<string, number> = {};
    const recentErrors: ErrorInfo[] = [];

    logs.forEach(log => {
      // 按类别统计
      errorsByCategory[log.category] = (errorsByCategory[log.category] || 0) + 1;
      
      // 按级别统计
      errorsByLevel[log.level] = (errorsByLevel[log.level] || 0) + 1;
      
      // 最近一小时的错误
      if (log.timestamp > oneHourAgo) {
        recentErrors.push(log);
      }
    });

    return {
      totalErrors: logs.length,
      errorsByCategory,
      errorsByLevel,
      recentErrors
    };
  }
}

// 创建全局错误日志实例
export const errorLogger = new ErrorLogger({
  enableRemote: false, // 暂时禁用远程上报
  remoteEndpoint: '/api/errors'
});

// 错误边界Hook
export const useErrorHandler = () => {
  const handleError = React.useCallback((error: Error, errorInfo?: any) => {
    errorLogger.logError({
      message: error.message,
      stack: error.stack,
      category: 'react',
      context: errorInfo
    });
  }, []);

  const handleAsyncError = React.useCallback(async (asyncFn: () => Promise<any>) => {
    try {
      return await asyncFn();
    } catch (error) {
      handleError(error as Error);
      throw error;
    }
  }, [handleError]);

  return { handleError, handleAsyncError };
};

// 性能监控
export const performanceLogger = {
  // 记录页面加载时间
  logPageLoad: () => {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        errorLogger.logInfo('Page Load Performance', {
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          totalTime: navigation.loadEventEnd - navigation.navigationStart
        });
      }
    }
  },

  // 记录API请求时间
  logApiRequest: (url: string, duration: number, status: number) => {
    errorLogger.logInfo('API Request', {
      url,
      duration,
      status,
      category: 'api-performance'
    });
  },

  // 记录组件渲染时间
  logComponentRender: (componentName: string, duration: number) => {
    errorLogger.logDebug('Component Render', {
      componentName,
      duration,
      category: 'render-performance'
    });
  }
};

export default errorLogger;
