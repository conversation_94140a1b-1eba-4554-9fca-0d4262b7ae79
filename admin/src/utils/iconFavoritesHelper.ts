import axiosInstance from '../api/axiosInstance';

export interface FavoriteFolder {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  color: string;
  icon: string;
  sort_order: number;
  is_default: boolean;
  icon_count: number;
  created_at: string;
  updated_at: string;
}

/**
 * 确保存在默认收藏夹
 */
export async function ensureDefaultFavoriteFolder(): Promise<FavoriteFolder> {
  try {
    // 获取所有收藏夹
    const foldersResponse = await axiosInstance.get('/favorites/folders');
    const folders: FavoriteFolder[] = foldersResponse.data || [];
    
    // 检查是否有默认收藏夹
    let defaultFolder = folders.find(f => f.is_default);
    
    if (!defaultFolder) {
      // 如果没有默认收藏夹，创建一个
      const createResponse = await axiosInstance.post('/favorites/folders', {
        name: 'default',
        display_name: '默认收藏夹',
        description: '系统自动创建的默认收藏夹',
        color: '#1890ff',
        icon: 'heart',
        sort_order: 0
      });
      
      if (createResponse.data && createResponse.data.folder) {
        defaultFolder = createResponse.data.folder;
        
        // 设置为默认收藏夹
        await axiosInstance.patch(`/favorites/folders/${defaultFolder.id}`, {
          is_default: true
        });
        
        defaultFolder.is_default = true;
      }
    }
    
    if (!defaultFolder) {
      throw new Error('无法创建或获取默认收藏夹');
    }
    
    return defaultFolder;
  } catch (error) {
    console.error('确保默认收藏夹失败:', error);
    throw error;
  }
}

/**
 * 添加图标到收藏夹
 */
export async function addIconToFavorites(iconId: number, folderId?: number): Promise<void> {
  try {
    let targetFolderId = folderId;
    
    if (!targetFolderId) {
      // 如果没有指定收藏夹，使用默认收藏夹
      const defaultFolder = await ensureDefaultFavoriteFolder();
      targetFolderId = defaultFolder.id;
    }
    
    await axiosInstance.post(`/favorites/folders/${targetFolderId}/icons`, {
      icon_id: iconId
    });
  } catch (error) {
    console.error('添加图标到收藏夹失败:', error);
    throw error;
  }
}

/**
 * 从收藏夹移除图标
 */
export async function removeIconFromFavorites(iconId: number): Promise<void> {
  try {
    // 获取图标所在的所有收藏夹
    const iconFoldersResponse = await axiosInstance.get(`/favorites/icons/${iconId}/folders`);
    const folders = iconFoldersResponse.data.folders || [];
    
    // 从每个收藏夹中删除
    for (const folder of folders) {
      await axiosInstance.delete(`/favorites/folders/${folder.id}/icons/${iconId}`);
    }
  } catch (error) {
    console.error('从收藏夹移除图标失败:', error);
    throw error;
  }
}

/**
 * 检查图标是否已收藏
 */
export async function isIconFavorited(iconId: number): Promise<boolean> {
  try {
    const iconFoldersResponse = await axiosInstance.get(`/favorites/icons/${iconId}/folders`);
    const folders = iconFoldersResponse.data.folders || [];
    return folders.length > 0;
  } catch (error) {
    console.error('检查图标收藏状态失败:', error);
    return false;
  }
}

/**
 * 获取所有收藏的图标
 */
export async function getAllFavoriteIcons(): Promise<any[]> {
  try {
    const foldersResponse = await axiosInstance.get('/favorites/folders');
    const folders: FavoriteFolder[] = foldersResponse.data || [];
    
    const allIcons: any[] = [];
    for (const folder of folders) {
      try {
        const iconsResponse = await axiosInstance.get(`/favorites/folders/${folder.id}/icons`, {
          params: { page: 1, page_size: 1000 }
        });
        if (iconsResponse.data && iconsResponse.data.icons) {
          allIcons.push(...iconsResponse.data.icons);
        }
      } catch (error) {
        console.error(`获取收藏夹 ${folder.id} 的图标失败:`, error);
      }
    }
    
    return allIcons;
  } catch (error) {
    console.error('获取所有收藏图标失败:', error);
    return [];
  }
}

/**
 * 初始化收藏夹系统
 */
export async function initializeFavoritesSystem(): Promise<void> {
  try {
    await ensureDefaultFavoriteFolder();
    // console.log('收藏夹系统初始化成功'); // 已清理调试代码
  } catch (error) {
    console.error('收藏夹系统初始化失败:', error);
    throw error;
  }
}
