import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Button, Table, Space, Modal, Form, Input, Select,
  message, Popconfirm, Tag, Switch, Tooltip, Tabs, Typography,
  Divider, Badge, Statistic
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined,
  SettingOutlined, FileTextOutlined, RocketOutlined, TagsOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import axiosInstance from '../api/axiosInstance';
import { patchBlog } from '../api/blogs';
import { useDebounce } from '../hooks/useDebounce';
import { EnhancedTable, EnhancedCard, EnhancedButton } from '../components/enhanced';

// 配置dayjs插件
dayjs.extend(utc);

const { Title } = Typography;
const { Search } = Input;
const { TabPane } = Tabs;

interface Article {
  id: number;
  title: string;
  slug: string;
  description?: string;
  author: string;
  article_type: 'blog' | 'project';
  display_date: string; // 展示日期，用户可编辑
  published: boolean;
  featured: boolean;
  show_on_homepage: boolean;
  // 系统时间戳（只读）
  created_at: string;
  updated_at: string;
  published_at?: string; // 发布时间
  tags: Array<{
    id: number;
    name: string;
    color?: string;
    category?: string;
  }>;
  // 项目特有字段
  project_url?: string;
  github_url?: string;
  demo_url?: string;
  project_status?: string;
}

const UnifiedArticleManagerPage: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('blog');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTag, setSelectedTag] = useState<string | undefined>(undefined);
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // 获取文章列表
  const { data: articles = [], isLoading } = useQuery({
    queryKey: ['articles', activeTab, debouncedSearchTerm, selectedTag],
    queryFn: async () => {
      const params: any = {
        article_type: activeTab,
        limit: 500,
        published_only: false
      };

      if (debouncedSearchTerm) {
        params.search = debouncedSearchTerm;
      }

      if (selectedTag) {
        params.tag = selectedTag;
      }

      const response = await axiosInstance.get('/blogs/', { params });
      return response.data;
    }
  });

  // 获取标签列表
  const { data: tags = [], isLoading: isLoadingTags } = useQuery({
    queryKey: ['tags'],
    queryFn: async () => {
      const response = await axiosInstance.get('/tags/');
      return response.data;
    }
  });

  // 获取文章总数（用于标签页计数）
  const { data: totalCounts = { blog: 0, project: 0 } } = useQuery({
    queryKey: ['articles-count'],
    queryFn: async () => {
      const [blogResponse, projectResponse] = await Promise.all([
        axiosInstance.get('/blogs/', {
          params: { article_type: 'blog', limit: 500, published_only: false }
        }),
        axiosInstance.get('/blogs/', {
          params: { article_type: 'project', limit: 500, published_only: false }
        })
      ]);

      return {
        blog: blogResponse.data.length,
        project: projectResponse.data.length
      };
    }
  });

  // 删除文章
  const deleteMutation = useMutation({
    mutationFn: async (slug: string) => {
      return axiosInstance.delete(`/blogs/${slug}`);
    },
    onSuccess: () => {
      message.success('删除成功');
      queryClient.invalidateQueries({ queryKey: ['articles'] });
      setSelectedRowKeys([]);
    },
    onError: (error: any) => {
      message.error(error.response?.data?.detail || '删除失败');
    }
  });

  // 批量删除
  const batchDeleteMutation = useMutation({
    mutationFn: async (slugs: string[]) => {
      return Promise.all(slugs.map(slug => axiosInstance.delete(`/blogs/${slug}`)));
    },
    onSuccess: () => {
      message.success('批量删除成功');
      queryClient.invalidateQueries({ queryKey: ['articles'] });
      setSelectedRowKeys([]);
    },
    onError: (error: any) => {
      message.error('批量删除失败');
    }
  });

  // 更新文章状态
  const updateStatusMutation = useMutation({
    mutationFn: async ({ slug, data }: { slug: string; data: any }) => {
      // console.log('更新文章状态:', slug, data); // 已清理调试代码
      return patchBlog(slug, data);
    },
    onSuccess: (data, variables) => {
      // console.log('更新成功:', data); // 已清理调试代码
      message.success('更新成功');
      queryClient.invalidateQueries({ queryKey: ['articles'] });
    },
    onError: (error: any) => {
      console.error('更新失败:', error);
      message.error(error.response?.data?.detail || '更新失败');
    }
  });

  // 处理新建
  const handleCreate = () => {
    if (activeTab === 'blog') {
      navigate('/articles/new?type=blog');
    } else {
      navigate('/articles/new?type=project');
    }
  };

  // 处理编辑
  const handleEdit = (record: Article) => {
    if (record.article_type === 'blog') {
      navigate(`/articles/edit/${record.slug}?type=blog`);
    } else {
      navigate(`/articles/edit/${record.slug}?type=project`);
    }
  };

  // 处理删除
  const handleDelete = (record: Article) => {
    deleteMutation.mutate(record.slug);
  };

  // 处理状态更新
  const handleStatusChange = (slug: string, field: string, value: boolean) => {
    updateStatusMutation.mutate({
      slug,
      data: { [field]: value }
    });
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  // 处理标签筛选
  const handleTagChange = (value: string | undefined) => {
    setSelectedTag(value);
  };

  // 当搜索词或标签改变时重置选中的行
  useEffect(() => {
    setSelectedRowKeys([]);
  }, [debouncedSearchTerm, selectedTag]);

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    // 切换标签页时清除搜索和筛选条件
    setSearchTerm('');
    setSelectedTag(undefined);
    setSelectedRowKeys([]);
  };

  // 处理批量删除
  const handleBatchDelete = () => {
    const slugs = selectedRowKeys.map(key => {
      const article = articles.find((a: Article) => a.id === key);
      return article?.slug;
    }).filter(Boolean) as string[];

    if (slugs.length === 0) {
      message.warning('请选择要删除的文章');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${slugs.length} 篇文章吗？此操作不可恢复。`,
      onOk: () => {
        batchDeleteMutation.mutate(slugs);
      }
    });
  };

  // 表格列定义
  const getColumns = () => {
    const baseColumns = [
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        render: (text: string, record: Article) => (
          <div>
            <div style={{ fontWeight: 'bold' }}>{text}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.slug}
            </div>
          </div>
        )
      },
      {
        title: '作者',
        dataIndex: 'author',
        key: 'author',
        width: 100
      },
      {
        title: '发布状态',
        dataIndex: 'published',
        key: 'published',
        width: 100,
        render: (published: boolean, record: Article) => (
          <Switch
            checkedChildren="已发布"
            unCheckedChildren="草稿"
            checked={published}
            loading={updateStatusMutation.isPending && updateStatusMutation.variables?.slug === record.slug && 'published' in updateStatusMutation.variables.data}
            onChange={(checked) => handleStatusChange(record.slug, 'published', checked)}
          />
        ),
      },
      {
        title: '主页显示',
        dataIndex: 'show_on_homepage',
        key: 'show_on_homepage',
        width: 100,
        render: (show: boolean, record: Article) => (
          <Switch
            checkedChildren="是"
            unCheckedChildren="否"
            checked={show}
            loading={updateStatusMutation.isPending && updateStatusMutation.variables?.slug === record.slug && 'show_on_homepage' in updateStatusMutation.variables.data}
            onChange={(checked) => handleStatusChange(record.slug, 'show_on_homepage', checked)}
          />
        ),
      },
      {
        title: '标签',
        dataIndex: 'tags',
        key: 'tags',
        width: 200,
        render: (tags: Article['tags']) => (
          <Space wrap>
            {tags?.slice(0, 3).map(tag => (
              <Tag key={tag.id} color={tag.color || 'blue'} size="small">
                {tag.name}
              </Tag>
            ))}
            {tags?.length > 3 && <Tag size="small">+{tags.length - 3}</Tag>}
          </Space>
        ),
        sorter: (a: Article, b: Article) => {
          const aTagsStr = a.tags?.map(tag => tag.name).join(',') || '';
          const bTagsStr = b.tags?.map(tag => tag.name).join(',') || '';
          return aTagsStr.localeCompare(bTagsStr);
        }
      },
      {
        title: '展示时间',
        dataIndex: 'display_date',
        key: 'display_date',
        width: 150,
        render: (date: string) => {
          if (!date) return '-';
          return dayjs.utc(date).format('YYYY-MM-DD HH:mm');
        },
        sorter: (a: Article, b: Article) => dayjs(a.display_date).unix() - dayjs(b.display_date).unix(),
        defaultSortOrder: 'descend' as const
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        width: 150,
        render: (date: string) => {
          if (!date) return '-';
          return dayjs.utc(date).format('YYYY-MM-DD HH:mm');
        },
        sorter: (a: Article, b: Article) => dayjs(a.updated_at).unix() - dayjs(b.updated_at).unix()
      },
      {
        title: '操作',
        key: 'actions',
        width: 150,
        render: (record: Article) => (
          <Space>
            <Tooltip title="编辑">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
            <Tooltip title="预览">
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={() => window.open(`http://**************:3000/${record.article_type === 'blog' ? 'blogs' : 'projects'}/${record.slug}`, '_blank')}
              />
            </Tooltip>
            <Popconfirm
              title="确定要删除这篇文章吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          </Space>
        )
      }
    ];

    // 为项目添加特有列
    if (activeTab === 'project') {
      baseColumns.splice(-3, 0, {
        title: '项目信息',
        key: 'project_info',
        width: 150,
        render: (record: Article) => (
          <Space direction="vertical" size="small">
            {record.project_status && (
              <Tag color={getProjectStatusColor(record.project_status)}>
                {getProjectStatusLabel(record.project_status)}
              </Tag>
            )}
            <Space>
              {record.project_url && (
                <Tooltip title="项目链接">
                  <Button type="link" size="small" href={record.project_url} target="_blank">
                    链接
                  </Button>
                </Tooltip>
              )}
              {record.github_url && (
                <Tooltip title="GitHub">
                  <Button type="link" size="small" href={record.github_url} target="_blank">
                    GitHub
                  </Button>
                </Tooltip>
              )}
            </Space>
          </Space>
        )
      });
    }

    return baseColumns;
  };

  // 获取项目状态颜色
  const getProjectStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      in_progress: 'processing',
      completed: 'success',
      paused: 'warning',
      cancelled: 'error'
    };
    return colors[status] || 'default';
  };

  // 获取项目状态标签
  const getProjectStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      in_progress: '进行中',
      completed: '已完成',
      paused: '已暂停',
      cancelled: '已取消'
    };
    return labels[status] || status;
  };

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  return (
    <div className="unified-article-manager">
      <EnhancedCard variant="elevated" borderAccent>
        <div style={{ marginBottom: '24px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} className="page-title" style={{ margin: 0 }}>
                文章管理
              </Title>
            </Col>
            <Col>
              <Space>
                <EnhancedButton
                  variant="secondary"
                  icon={<TagsOutlined />}
                  onClick={() => navigate('/articles/tags')}
                >
                  标签管理
                </EnhancedButton>
                {selectedRowKeys.length > 0 && (
                  <EnhancedButton
                    variant="error"
                    onClick={handleBatchDelete}
                    loading={batchDeleteMutation.isPending}
                  >
                    批量删除 ({selectedRowKeys.length})
                  </EnhancedButton>
                )}
                <EnhancedButton
                  variant="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreate}
                  glowing
                >
                  新建{activeTab === 'blog' ? '博客' : '项目'}
                </EnhancedButton>
              </Space>
            </Col>
          </Row>
        </div>

        {/* 搜索和筛选区域 */}
        <div style={{ marginBottom: '16px' }}>
          <Row gutter={16} align="middle">
            <Col flex="auto">
              <Space wrap>
                {/* 标签筛选 */}
                <Select
                  placeholder="按标签筛选"
                  allowClear
                  style={{ width: 180 }}
                  onChange={handleTagChange}
                  loading={isLoadingTags}
                  value={selectedTag}
                >
                  {tags.map((tag: any) => (
                    <Select.Option key={tag.slug || tag.id} value={tag.slug || tag.id}>
                      <Tag color={tag.color || 'blue'} size="small" style={{ margin: 0 }}>
                        {tag.name}
                      </Tag>
                    </Select.Option>
                  ))}
                </Select>

                {/* 搜索输入 */}
                <Search
                  placeholder={`搜索${activeTab === 'blog' ? '博客' : '项目'}标题...`}
                  onSearch={handleSearch}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{ width: 300 }}
                  allowClear
                  value={searchTerm}
                />
              </Space>
            </Col>

            {/* 搜索结果统计 */}
            <Col>
              <Space>
                {(debouncedSearchTerm || selectedTag) && (
                  <Tag color="blue">
                    找到 {articles.length} 个结果
                  </Tag>
                )}
              </Space>
            </Col>
          </Row>
        </div>

        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          items={[
            {
              key: 'blog',
              label: (
                <Space>
                  <FileTextOutlined />
                  博客文章
                  <Badge count={activeTab === 'blog' ? articles.length : totalCounts.blog} />
                </Space>
              ),
              children: (
                <EnhancedTable
                  variant="elevated"
                  interactive
                  stickyHeader
                  rowSelection={rowSelection}
                  columns={getColumns()}
                  dataSource={articles}
                  rowKey="id"
                  loading={isLoading}
                  pagination={{
                    pageSize: 20,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 篇博客`
                  }}
                />
              )
            },
            {
              key: 'project',
              label: (
                <Space>
                  <RocketOutlined />
                  项目展示
                  <Badge count={activeTab === 'project' ? articles.length : totalCounts.project} />
                </Space>
              ),
              children: (
                <EnhancedTable
                  variant="elevated"
                  interactive
                  stickyHeader
                  rowSelection={rowSelection}
                  columns={getColumns()}
                  dataSource={articles}
                  rowKey="id"
                  loading={isLoading}
                  pagination={{
                    pageSize: 20,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 个项目`
                  }}
                />
              )
            }
          ]}
        />
      </EnhancedCard>
    </div>
  );
};

export default UnifiedArticleManagerPage;
