import React, { useState, useEffect } from 'react';
import { Card, Switch, Button, Form, Input, Select, Space, message, Divider } from 'antd';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { HolderOutlined, EyeOutlined, EyeInvisibleOutlined, SettingOutlined } from '@ant-design/icons';

interface LayoutBlock {
  id: string;
  name: string;
  enabled: boolean;
  order: number;
  config: {
    title?: string;
    description?: string;
    showTitle?: boolean;
    backgroundColor?: string;
    padding?: string;
    margin?: string;
  };
}

const defaultBlocks: LayoutBlock[] = [
  {
    id: 'hero',
    name: '个人信息区块',
    enabled: true,
    order: 1,
    config: {
      title: '个人介绍',
      showTitle: false,
      backgroundColor: 'transparent',
      padding: '24px',
      margin: '0'
    }
  },
  {
    id: 'tech-stack',
    name: '技术栈图标云',
    enabled: true,
    order: 2,
    config: {
      title: '技术栈',
      showTitle: false,
      backgroundColor: 'transparent',
      padding: '24px',
      margin: '24px 0'
    }
  },
  {
    id: 'github-contributions',
    name: 'GitHub贡献图',
    enabled: true,
    order: 3,
    config: {
      title: 'GitHub活动',
      showTitle: false,
      backgroundColor: 'transparent',
      padding: '16px',
      margin: '16px 0'
    }
  },
  {
    id: 'featured-projects',
    name: '精选项目',
    enabled: true,
    order: 4,
    config: {
      title: '精选项目',
      showTitle: true,
      backgroundColor: 'transparent',
      padding: '32px 0',
      margin: '32px 0'
    }
  },
  {
    id: 'blog-posts',
    name: '博客文章',
    enabled: true,
    order: 5,
    config: {
      title: '最新文章',
      showTitle: true,
      backgroundColor: 'transparent',
      padding: '32px 0',
      margin: '32px 0'
    }
  },
  {
    id: 'activity-feed',
    name: '活动动态',
    enabled: true,
    order: 6,
    config: {
      title: '最近动态',
      showTitle: true,
      backgroundColor: 'transparent',
      padding: '32px 0',
      margin: '32px 0'
    }
  }
];

const LayoutManagerPage: React.FC = () => {
  const [blocks, setBlocks] = useState<LayoutBlock[]>(defaultBlocks);
  const [selectedBlock, setSelectedBlock] = useState<LayoutBlock | null>(null);
  const [form] = Form.useForm();

  // 处理拖拽结束
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(blocks);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // 更新order字段
    const updatedItems = items.map((item, index) => ({
      ...item,
      order: index + 1
    }));

    setBlocks(updatedItems);
  };

  // 切换区块启用状态
  const toggleBlockEnabled = (blockId: string) => {
    setBlocks(blocks.map(block => 
      block.id === blockId 
        ? { ...block, enabled: !block.enabled }
        : block
    ));
  };

  // 选择区块进行配置
  const selectBlock = (block: LayoutBlock) => {
    setSelectedBlock(block);
    form.setFieldsValue(block.config);
  };

  // 保存区块配置
  const saveBlockConfig = (values: any) => {
    if (!selectedBlock) return;

    setBlocks(blocks.map(block =>
      block.id === selectedBlock.id
        ? { ...block, config: { ...block.config, ...values } }
        : block
    ));

    message.success('区块配置已保存');
    setSelectedBlock(null);
  };

  // 保存整体布局
  const saveLayout = async () => {
    try {
      // 这里应该调用API保存布局配置
      // console.log('保存布局配置:', blocks); // 已清理调试代码
      message.success('布局配置已保存');
    } catch (error) {
      message.error('保存失败');
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>页面布局管理</h1>
        <Button type="primary" onClick={saveLayout}>
          保存布局
        </Button>
      </div>

      <div style={{ display: 'flex', gap: '24px' }}>
        {/* 左侧：区块列表 */}
        <div style={{ flex: 2 }}>
          <Card title="页面区块" size="small">
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="blocks">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {blocks.map((block, index) => (
                      <Draggable key={block.id} draggableId={block.id} index={index}>
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            style={{
                              ...provided.draggableProps.style,
                              marginBottom: '8px'
                            }}
                          >
                            <Card
                              size="small"
                              style={{
                                backgroundColor: snapshot.isDragging ? '#f0f0f0' : 'white',
                                opacity: block.enabled ? 1 : 0.6
                              }}
                            >
                              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                  <div {...provided.dragHandleProps}>
                                    <HolderOutlined style={{ cursor: 'grab' }} />
                                  </div>
                                  <span>{block.name}</span>
                                </div>
                                <Space>
                                  <Switch
                                    size="small"
                                    checked={block.enabled}
                                    onChange={() => toggleBlockEnabled(block.id)}
                                    checkedChildren={<EyeOutlined />}
                                    unCheckedChildren={<EyeInvisibleOutlined />}
                                  />
                                  <Button
                                    size="small"
                                    icon={<SettingOutlined />}
                                    onClick={() => selectBlock(block)}
                                  />
                                </Space>
                              </div>
                            </Card>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </Card>
        </div>

        {/* 右侧：区块配置 */}
        <div style={{ flex: 1 }}>
          <Card title="区块配置" size="small">
            {selectedBlock ? (
              <Form
                form={form}
                layout="vertical"
                onFinish={saveBlockConfig}
              >
                <Form.Item label="区块名称">
                  <Input value={selectedBlock.name} disabled />
                </Form.Item>

                <Form.Item name="title" label="显示标题">
                  <Input placeholder="区块标题" />
                </Form.Item>

                <Form.Item name="showTitle" label="显示标题" valuePropName="checked">
                  <Switch />
                </Form.Item>

                <Form.Item name="description" label="描述">
                  <Input.TextArea rows={3} placeholder="区块描述" />
                </Form.Item>

                <Divider>样式配置</Divider>

                <Form.Item name="backgroundColor" label="背景颜色">
                  <Select>
                    <Select.Option value="transparent">透明</Select.Option>
                    <Select.Option value="#ffffff">白色</Select.Option>
                    <Select.Option value="#f5f5f5">浅灰</Select.Option>
                    <Select.Option value="#fafafa">极浅灰</Select.Option>
                  </Select>
                </Form.Item>

                <Form.Item name="padding" label="内边距">
                  <Select>
                    <Select.Option value="0">无</Select.Option>
                    <Select.Option value="16px">小</Select.Option>
                    <Select.Option value="24px">中</Select.Option>
                    <Select.Option value="32px">大</Select.Option>
                    <Select.Option value="48px">特大</Select.Option>
                  </Select>
                </Form.Item>

                <Form.Item name="margin" label="外边距">
                  <Select>
                    <Select.Option value="0">无</Select.Option>
                    <Select.Option value="16px 0">上下小</Select.Option>
                    <Select.Option value="24px 0">上下中</Select.Option>
                    <Select.Option value="32px 0">上下大</Select.Option>
                    <Select.Option value="48px 0">上下特大</Select.Option>
                  </Select>
                </Form.Item>

                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit">
                      保存配置
                    </Button>
                    <Button onClick={() => setSelectedBlock(null)}>
                      取消
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            ) : (
              <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                选择一个区块进行配置
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
};

export default LayoutManagerPage;
