import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message, Spin, Alert } from 'antd';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getPersonalInfo, updatePersonalInfo, PersonalInfoConfig } from '../api/siteSettings';
import { log } from '../utils/logger';

const { TextArea } = Input;

const PersonalInfoPage: React.FC = () => {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const [hasChanges, setHasChanges] = useState(false);

  // 获取个人信息
  const { data, isLoading, error } = useQuery({
    queryKey: ['personalInfo'],
    queryFn: getPersonalInfo,
  });

  // 更新个人信息
  const updateMutation = useMutation({
    mutationFn: updatePersonalInfo,
    onSuccess: () => {
      message.success('个人信息更新成功！');
      setHasChanges(false);
      queryClient.invalidateQueries({ queryKey: ['personalInfo'] });
    },
    onError: (error: any) => {
      log.error('Personal info update failed', error, 'PERSONAL_INFO');
      message.error('更新失败，请稍后重试');
    },
  });

  // 当数据加载完成时，设置表单初始值
  useEffect(() => {
    // console.log('PersonalInfoPage - Data loaded:', data); // 已清理调试代码
    if (data?.personal_info) {
      // console.log('PersonalInfoPage - Setting form values:', data.personal_info); // 已清理调试代码
      form.setFieldsValue(data.personal_info);
    }
  }, [data, form]);

  // 监听表单值变化
  const handleValuesChange = () => {
    setHasChanges(true);
  };

  // 提交表单
  const handleSubmit = async (values: PersonalInfoConfig) => {
    try {
      await updateMutation.mutateAsync(values);
    } catch (error) {
      console.error('提交失败:', error);
    }
  };

  // 重置表单
  const handleReset = () => {
    if (data?.personal_info) {
      form.setFieldsValue(data.personal_info);
      setHasChanges(false);
    }
  };

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: '16px' }}>加载个人信息中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="加载失败"
        description="无法加载个人信息，请刷新页面重试"
        type="error"
        showIcon
      />
    );
  }

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto' }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        onValuesChange={handleValuesChange}
        initialValues={data?.personal_info}
      >
          <Form.Item
            label="姓名"
            name="name"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="请输入您的姓名" />
          </Form.Item>

          <Form.Item
            label="个人标题"
            name="headline"
            rules={[{ required: true, message: '请输入个人标题' }]}
          >
            <Input placeholder="例如：Master's candidate in Information Science" />
          </Form.Item>

          <Form.Item
            label="个人介绍"
            name="introduction"
            rules={[{ required: true, message: '请输入个人介绍' }]}
          >
            <TextArea
              rows={4}
              placeholder="请输入您的个人介绍，这将显示在首页"
            />
          </Form.Item>



          <Form.Item>
            <div style={{ display: 'flex', gap: '12px' }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={updateMutation.isPending}
                disabled={!hasChanges}
              >
                {updateMutation.isPending ? '保存中...' : '保存更改'}
              </Button>
              <Button onClick={handleReset} disabled={!hasChanges}>
                重置
              </Button>
            </div>
          </Form.Item>

          {hasChanges && (
            <Alert
              message="您有未保存的更改"
              description="请点击'保存更改'按钮保存您的修改"
              type="warning"
              showIcon
              style={{ marginTop: '16px' }}
            />
          )}
        </Form>
    </div>
  );
};

export default PersonalInfoPage;
