import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  message,
  Space,
  Typography,
  Divider,
  Row,
  Col,
  Spin,
  Alert,
  Tabs
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  EditOutlined,
  EyeOutlined,
  FileTextOutlined,
  FolderOpenOutlined,
  PictureOutlined
} from '@ant-design/icons';
import { getPagesConfig, updatePagesConfig, PagesConfig } from '../api/siteSettings';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;

const PagesConfigManagerPage: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [initialData, setInitialData] = useState<PagesConfig | null>(null);

  // 加载页面配置
  const loadPagesConfig = async () => {
    setLoading(true);
    try {
      const response = await getPagesConfig();
      const data = response.pages_config;
      setInitialData(data);
      form.setFieldsValue(data);
      message.success('页面配置加载成功');
    } catch (error) {
      console.error('Failed to load pages config:', error);
      message.error('加载页面配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存页面配置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setSaving(true);
      
      // console.log('Saving pages config:', values); // 已清理调试代码
      const response = await updatePagesConfig(values);
      // console.log('Save response:', response); // 已清理调试代码
      
      setInitialData(values);
      message.success('页面配置保存成功！');
    } catch (error) {
      console.error('Failed to save pages config:', error);
      message.error(`保存页面配置失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setSaving(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    if (initialData) {
      form.setFieldsValue(initialData);
      message.info('已重置为上次保存的配置');
    }
  };

  // 预览前端效果
  const handlePreview = (page: string) => {
    const urls = {
      blogs: 'http://localhost:3001/blogs',
      projects: 'http://localhost:3001/projects',
      gallery: 'http://localhost:3001/gallery'
    };
    window.open(urls[page as keyof typeof urls], '_blank');
  };

  useEffect(() => {
    loadPagesConfig();
  }, []);

  const pageConfigs = [
    {
      key: 'blogs',
      title: '博客页面',
      icon: <FileTextOutlined />,
      description: '管理博客列表页面的标题和描述'
    },
    {
      key: 'projects',
      title: '项目页面',
      icon: <FolderOpenOutlined />,
      description: '管理项目展示页面的标题和描述'
    },
    {
      key: 'gallery',
      title: '图库页面',
      icon: <PictureOutlined />,
      description: '管理图片画廊页面的标题和描述'
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <EditOutlined />
            <span>页面配置管理</span>
          </Space>
        }
        extra={
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadPagesConfig}
              loading={loading}
            >
              重新加载
            </Button>
          </Space>
        }
      >
        <Alert
          message="页面配置管理"
          description="在这里可以修改 /blogs、/projects、/gallery 页面的标题和描述，修改后会立即在前端生效。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Spin spinning={loading}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSave}
          >
            <Tabs defaultActiveKey="blogs" type="card">
              {pageConfigs.map((config) => (
                <TabPane
                  tab={
                    <Space>
                      {config.icon}
                      {config.title}
                    </Space>
                  }
                  key={config.key}
                >
                  <Card 
                    size="small" 
                    title={config.title}
                    extra={
                      <Button 
                        icon={<EyeOutlined />} 
                        size="small"
                        onClick={() => handlePreview(config.key)}
                      >
                        预览页面
                      </Button>
                    }
                    style={{ marginBottom: 16 }}
                    type="inner"
                  >
                    <Text type="secondary" style={{ marginBottom: 16, display: 'block' }}>
                      {config.description}
                    </Text>
                    
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name={[config.key, 'title']}
                          label="页面标题"
                          rules={[{ required: true, message: '请输入页面标题' }]}
                        >
                          <Input placeholder={`如：${config.title}`} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name={[config.key, 'description']}
                          label="页面描述"
                          rules={[{ required: true, message: '请输入页面描述' }]}
                        >
                          <TextArea 
                            rows={4}
                            placeholder={`${config.title}的详细描述`}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                </TabPane>
              ))}
            </Tabs>

            {/* 操作按钮 */}
            <div style={{ textAlign: 'center', marginTop: 24 }}>
              <Space size="large">
                <Button 
                  type="primary" 
                  icon={<SaveOutlined />}
                  loading={saving}
                  htmlType="submit"
                  size="large"
                >
                  保存所有配置
                </Button>
                <Button 
                  onClick={handleReset}
                  size="large"
                >
                  重置
                </Button>
              </Space>
            </div>
          </Form>
        </Spin>
      </Card>
    </div>
  );
};

export default PagesConfigManagerPage;
