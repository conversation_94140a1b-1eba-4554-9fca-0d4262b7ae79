import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  message,
  Space,
  Typography,
  Divider,
  Row,
  Col,
  Spin,
  Alert
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  EditOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { getHomepageSections, updateHomepageSections, HomepageSectionsConfig } from '../api/siteSettings';

const { Title, Text } = Typography;
const { TextArea } = Input;

const HomepageContentManagerPage: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [initialData, setInitialData] = useState<HomepageSectionsConfig | null>(null);

  // 加载主页内容配置
  const loadHomepageContent = async () => {
    setLoading(true);
    try {
      const response = await getHomepageSections();
      const data = response.homepage_sections;
      setInitialData(data);
      form.setFieldsValue(data);
      message.success('主页内容配置加载成功');
    } catch (error) {
      console.error('Failed to load homepage content:', error);
      message.error('加载主页内容配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存主页内容配置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setSaving(true);

      // console.log('Saving homepage sections:', values); // 已清理调试代码
      const response = await updateHomepageSections(values);
      // console.log('Save response:', response); // 已清理调试代码

      setInitialData(values);
      message.success('主页内容配置保存成功！');
    } catch (error) {
      console.error('Failed to save homepage content:', error);
      message.error(`保存主页内容配置失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setSaving(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    if (initialData) {
      form.setFieldsValue(initialData);
      message.info('已重置为上次保存的配置');
    }
  };

  // 预览前端效果
  const handlePreview = () => {
    const frontendUrl = import.meta.env.VITE_FRONTEND_URL || 'http://localhost:3000';
    window.open(frontendUrl, '_blank');
  };

  useEffect(() => {
    loadHomepageContent();
  }, []);

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <EditOutlined />
            <span>主页内容管理</span>
          </Space>
        }
        extra={
          <Space>
            <Button 
              icon={<EyeOutlined />} 
              onClick={handlePreview}
            >
              预览前端
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadHomepageContent}
              loading={loading}
            >
              重新加载
            </Button>
          </Space>
        }
      >
        <Alert
          message="主页内容配置"
          description="在这里可以修改前端主页各个区域的标题和描述文本，修改后会立即在前端生效。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Spin spinning={loading}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSave}
          >
            {/* 项目区域配置 */}
            <Card 
              size="small" 
              title="项目区域配置" 
              style={{ marginBottom: 16 }}
              type="inner"
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="projectHeadLine"
                    label="项目区域标题"
                    rules={[{ required: true, message: '请输入项目区域标题' }]}
                  >
                    <Input placeholder="如：Work" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="projectIntro"
                    label="项目区域描述"
                    rules={[{ required: true, message: '请输入项目区域描述' }]}
                  >
                    <TextArea 
                      rows={3}
                      placeholder="项目区域的介绍文本"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* 博客区域配置 */}
            <Card 
              size="small" 
              title="博客区域配置" 
              style={{ marginBottom: 16 }}
              type="inner"
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="blogHeadLine"
                    label="博客区域标题"
                    rules={[{ required: true, message: '请输入博客区域标题' }]}
                  >
                    <Input placeholder="如：What I've been thinking about." />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="blogIntro"
                    label="博客区域描述"
                    rules={[{ required: true, message: '请输入博客区域描述' }]}
                  >
                    <TextArea 
                      rows={3}
                      placeholder="如：I've written something about programming and life."
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* 活动区域配置 */}
            <Card 
              size="small" 
              title="活动区域配置" 
              style={{ marginBottom: 24 }}
              type="inner"
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="activityHeadLine"
                    label="活动区域标题"
                    rules={[{ required: true, message: '请输入活动区域标题' }]}
                  >
                    <Input placeholder="如：Recent Activities" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="activityIntro"
                    label="活动区域描述"
                    rules={[{ required: true, message: '请输入活动区域描述' }]}
                  >
                    <TextArea 
                      rows={3}
                      placeholder="如：What I've been up to recently."
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* 操作按钮 */}
            <div style={{ textAlign: 'center' }}>
              <Space size="large">
                <Button 
                  type="primary" 
                  icon={<SaveOutlined />}
                  loading={saving}
                  htmlType="submit"
                  size="large"
                >
                  保存配置
                </Button>
                <Button 
                  onClick={handleReset}
                  size="large"
                >
                  重置
                </Button>
              </Space>
            </div>
          </Form>
        </Spin>
      </Card>
    </div>
  );
};

export default HomepageContentManagerPage;
