import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { message, Spin } from 'antd';
import { useQuery } from '@tanstack/react-query';
import UniversalContentEditor from '../components/universal/UniversalContentEditor';
import { BLOG_CONFIG, PROJECT_CONFIG } from '../config/contentTypes';
import axiosInstance from '../api/axiosInstance';

const UnifiedArticleEditPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const type = searchParams.get('type') || 'blog';


  // 获取文章数据
  const { data: articleData, isLoading } = useQuery({
    queryKey: ['article', slug],
    queryFn: async () => {
      if (!slug) return null;
      const response = await axiosInstance.get(`/blogs/${slug}`);
      return response.data;
    },
    enabled: !!slug
  });

  // 处理保存
  const handleSave = async (data: any) => {
    try {
      // 确保article_type字段正确设置
      const saveData = {
        ...data,
        article_type: type,
        // 确保必需字段存在
        display_date: data.display_date || new Date().toISOString(),
        author: data.author || 'Unknown',
        // 如果是项目类型，确保必要的项目字段存在
        ...(type === 'project' && {
          project_status: data.project_status || 'completed',
          featured: data.featured || false,
          is_open_source: data.is_open_source || false,
          display_order: data.display_order || 0
        })
      };

      if (slug) {
        // 更新现有文章
        await axiosInstance.put(`/blogs/${slug}`, saveData);
        message.success('文章更新成功！');

        // 清除前端缓存（异步执行，不阻塞保存流程）
        fetch('http://localhost:3001/api/revalidate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            tag: 'blogs',
            path: '/blogs'
          })
        }).catch(error => {
          console.warn('清除前端缓存失败:', error);
        });
      } else {
        // 创建新文章
        await axiosInstance.post('/blogs/', saveData);
        message.success('文章创建成功！');

        // 清除前端缓存（异步执行，不阻塞保存流程）
        fetch('http://localhost:3001/api/revalidate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            tag: 'blogs',
            path: '/blogs'
          })
        }).catch(error => {
          console.warn('清除前端缓存失败:', error);
        });
      }

      // 保存成功后直接导航
      navigate('/articles');
    } catch (error: any) {
      console.error('保存失败:', error);
      message.error(error.response?.data?.detail || '保存失败，请稍后重试');
    }
  };

  // 处理取消
  const handleCancel = () => {
    navigate('/articles');
  };

  // 处理预览
  const handlePreview = (data: any) => {
    // 可以实现预览功能
    // console.log('预览数据:', data); // 已清理调试代码
  };

  // 获取内容类型配置
  const getConfig = () => {
    return type === 'project' ? PROJECT_CONFIG : BLOG_CONFIG;
  };

  // 转换数据格式以适配编辑器
  const getInitialData = () => {
    if (!articleData) return undefined;

    return {
      title: articleData.title,
      description: articleData.description,
      author: articleData.author,
      content: articleData.content,
      display_date: articleData.display_date,
      published: articleData.published,
      show_on_homepage: articleData.show_on_homepage,
      article_type: articleData.article_type,
      tag_ids: articleData.tags?.map((tag: any) => tag.id) || [],
      
      // SEO字段
      meta_title: articleData.meta_title,
      meta_description: articleData.meta_description,
      meta_keywords: articleData.meta_keywords,
      og_title: articleData.og_title,
      og_description: articleData.og_description,
      og_image: articleData.og_image,
      canonical_url: articleData.canonical_url,
      
      // 项目特有字段
      ...(articleData.article_type === 'project' && {
        project_url: articleData.project_url,
        github_url: articleData.github_url,
        demo_url: articleData.demo_url,
        logo_url: articleData.logo_url,
        icon: articleData.icon,
        project_status: articleData.project_status,
        is_github_project: articleData.is_github_project,
        is_open_source: articleData.is_open_source,
        featured: articleData.featured,
        display_order: articleData.display_order
      })
    };
  };

  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px'
      }}>
        <Spin size="large">
          <div style={{ padding: '50px' }}>加载文章数据中...</div>
        </Spin>
      </div>
    );
  }

  return (
    <div className="unified-article-edit-page">
      <UniversalContentEditor
        contentType={type as 'blog' | 'project'}
        contentId={slug}
        mode={slug ? 'edit' : 'create'}
        initialData={getInitialData()}
        onSave={handleSave}
        onCancel={handleCancel}
        onPreview={handlePreview}
        customConfig={getConfig()}
      />
    </div>
  );
};

export default UnifiedArticleEditPage;
