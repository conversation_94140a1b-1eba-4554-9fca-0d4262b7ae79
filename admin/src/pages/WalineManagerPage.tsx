import React, { useState, useEffect } from 'react';
import { Card, Alert, Button, Space, Spin, Input, Form, Modal, message } from 'antd';
import {
  MessageOutlined,
  LinkOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { getWalineConfig, setWalineConfig, WalineConfig } from '../api/walineConfig';

const WalineManagerPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [iframeKey, setIframeKey] = useState(0);
  const [iframeError, setIframeError] = useState(false);
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [walineConfig, setWalineConfigState] = useState<WalineConfig>({
    server_url: 'https://waline.jyaochen.cn',
    admin_url: 'https://waline.jyaochen.cn/ui'
  });
  const [form] = Form.useForm();

  // 加载Waline配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const config = await getWalineConfig();
        setWalineConfigState(config);
        form.setFieldsValue(config);
      } catch (error) {
        console.error('加载Waline配置失败:', error);
        message.error('加载Waline配置失败');
      }
    };

    loadConfig();
  }, [form]);

  useEffect(() => {
    // 检测iframe是否能正常加载
    const timer = setTimeout(() => {
      if (loading) {
        setLoading(false);
        // 如果超时仍在加载，可能是iframe被阻止了
        setIframeError(true);
      }
    }, 5000);

    return () => clearTimeout(timer);
  }, [loading]);

  const handleReload = () => {
    setLoading(true);
    setIframeError(false);
    setIframeKey(prev => prev + 1);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  const handleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const openInNewTab = () => {
    const newWindow = window.open(walineConfig.admin_url, '_blank', 'noopener,noreferrer');
    if (!newWindow) {
      // 如果弹窗被阻止，提供备用方案
      const link = document.createElement('a');
      link.href = walineConfig.admin_url;
      link.target = '_blank';
      link.rel = 'noopener noreferrer';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleConfigSave = async () => {
    try {
      const values = await form.validateFields();
      await setWalineConfig(values);
      setWalineConfigState(values);
      setConfigModalVisible(false);
      message.success('Waline配置保存成功');
      // 重新加载iframe
      handleReload();
    } catch (error) {
      console.error('保存Waline配置失败:', error);
      message.error('保存Waline配置失败');
    }
  };

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50 bg-white' : 'p-6'}`}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold flex items-center">
            <MessageOutlined className="mr-2" />
            Waline 评论管理
          </h1>
          <p className="text-gray-600 mt-1">直接访问 Waline 官方管理界面</p>
        </div>
        
        <Space>
          <Button
            icon={<SettingOutlined />}
            onClick={() => setConfigModalVisible(true)}
          >
            配置
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleReload}
            loading={loading}
          >
            刷新
          </Button>
          <Button
            icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
            onClick={handleFullscreen}
          >
            {isFullscreen ? '退出全屏' : '全屏显示'}
          </Button>
          <Button
            type="primary"
            icon={<LinkOutlined />}
            onClick={openInNewTab}
          >
            新窗口打开
          </Button>
        </Space>
      </div>

      <Alert
        message="Waline 管理界面"
        description={
          <div>
            <p>这里直接嵌入了 Waline 官方管理界面，您可以在此处管理所有评论。</p>
            <p>如果页面显示异常，请点击"新窗口打开"按钮在新标签页中访问。</p>
            <p>
              <strong>直接访问链接：</strong>
              <a
                href={walineConfig.admin_url}
                target="_blank"
                rel="noopener noreferrer"
                className="ml-2 text-blue-600 hover:text-blue-800"
              >
                {walineConfig.admin_url}
              </a>
            </p>
          </div>
        }
        type="info"
        showIcon
        className="mb-4"
      />

      <Card
        className={`${isFullscreen ? 'h-full' : ''}`}
        bodyStyle={{
          padding: 0,
          height: isFullscreen ? 'calc(100vh - 200px)' : '800px',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
            <Spin size="large" />
          </div>
        )}

        {iframeError ? (
          <div className="flex flex-col items-center justify-center h-full text-center p-8">
            <div className="text-6xl mb-4">💬</div>
            <h3 className="text-xl font-semibold mb-4">Waline 评论管理</h3>
            <div className="max-w-md mx-auto space-y-4">
              <p className="text-gray-600">
                由于浏览器安全策略限制，无法直接嵌入Waline管理界面。
              </p>
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-2">访问方式：</h4>
                <ol className="text-left text-sm space-y-1">
                  <li>1. 点击下方按钮在新窗口中打开管理界面</li>
                  <li>2. 或直接访问：<code className="bg-gray-100 px-1 rounded">{walineConfig.admin_url}</code></li>
                  <li>3. 使用您的管理员账号登录即可管理评论</li>
                </ol>
              </div>
              <Space direction="vertical" size="middle">
                <Button
                  type="primary"
                  icon={<LinkOutlined />}
                  onClick={openInNewTab}
                  size="large"
                  block
                >
                  在新窗口中打开 Waline 管理界面
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleReload}
                  block
                >
                  重新尝试嵌入
                </Button>
              </Space>
            </div>
          </div>
        ) : (
          <div className="w-full h-full">
            <iframe
              key={iframeKey}
              src={walineConfig.admin_url}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                display: 'block',
                minHeight: '600px'
              }}
              title="Waline 管理界面"
              onLoad={() => {
                setLoading(false);
                // 检查iframe是否正常加载
                setTimeout(() => {
                  try {
                    const iframe = document.querySelector('iframe[title="Waline 管理界面"]') as HTMLIFrameElement;
                    if (iframe && iframe.contentWindow) {
                      // 如果可以访问contentWindow，说明加载成功
                      // console.log('Waline 管理界面加载成功'); // 已清理调试代码
                    }
                  } catch (error) {
                    console.warn('无法访问iframe内容，可能由于跨域限制');
                  }
                }, 2000);
              }}
              onError={() => {
                setLoading(false);
                setIframeError(true);
                console.error('Waline 管理界面加载失败');
              }}
              allow="same-origin scripts forms popups popups-to-escape-sandbox navigation-api"
              referrerPolicy="no-referrer-when-downgrade"
            />
          </div>
        )}
      </Card>

      {isFullscreen && (
        <div className="fixed bottom-4 right-4 z-60">
          <Button
            type="primary"
            icon={<FullscreenExitOutlined />}
            onClick={handleFullscreen}
            size="large"
          >
            退出全屏
          </Button>
        </div>
      )}

      {/* 配置模态框 */}
      <Modal
        title="Waline 配置"
        open={configModalVisible}
        onOk={handleConfigSave}
        onCancel={() => setConfigModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={walineConfig}
        >
          <Form.Item
            label="Waline 服务器地址"
            name="server_url"
            rules={[
              { required: true, message: '请输入Waline服务器地址' },
              { type: 'url', message: '请输入有效的URL地址' }
            ]}
          >
            <Input
              placeholder="https://waline.jyaochen.cn"
              onChange={(e) => {
                const serverUrl = e.target.value;
                if (serverUrl) {
                  // 自动生成管理界面地址
                  const adminUrl = `${serverUrl.replace(/\/$/, '')}/ui`;
                  form.setFieldValue('admin_url', adminUrl);
                }
              }}
            />
          </Form.Item>

          <Form.Item
            label="管理界面地址"
            name="admin_url"
            rules={[
              { required: true, message: '请输入管理界面地址' },
              { type: 'url', message: '请输入有效的URL地址' }
            ]}
          >
            <Input placeholder="https://waline.jyaochen.cn/ui" />
          </Form.Item>

          <Alert
            message="配置说明"
            description={
              <ul className="text-sm mt-2 space-y-1">
                <li>• 服务器地址：您的Waline评论系统部署地址</li>
                <li>• 管理界面地址：通常是服务器地址 + /ui</li>
                <li>• 修改后将应用到前端和后台管理界面</li>
              </ul>
            }
            type="info"
            showIcon
            className="mt-4"
          />
        </Form>
      </Modal>
    </div>
  );
};

export default WalineManagerPage;
