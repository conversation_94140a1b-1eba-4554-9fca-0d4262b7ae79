import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { message, Modal } from 'antd';
import { useQuery, useMutation } from '@tanstack/react-query';
import axiosInstance from '../api/axiosInstance';
import UniversalContentEditor from '../components/universal/UniversalContentEditor';
import ContentPreview from '../components/universal/ContentPreview';
import { ContentTypeConfig } from '../types/universal';
import dayjs from 'dayjs';

interface WebsiteVersionData {
  id?: number;
  version: string;
  title: string;
  content: string;
  release_date: string;
  is_published: boolean;
  is_major: boolean;
  author?: string;
  tags?: string;
}

const WebsiteVersionEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEdit = Boolean(id && id !== 'new');
  const mode = isEdit ? 'edit' : 'create';
  const [loading, setLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewData, setPreviewData] = useState<any>(null);

  // 获取版本数据（编辑模式）
  const { data: initialData, isLoading: isLoadingData } = useQuery({
    queryKey: ['website-version', id],
    queryFn: async () => {
      if (!isEdit) return null;
      const response = await axiosInstance.get(`/website-versions/${id}`);
      return response.data;
    },
    enabled: isEdit,
  });

  // 保存版本
  const saveMutation = useMutation({
    mutationFn: async (data: WebsiteVersionData) => {
      if (isEdit) {
        return await axiosInstance.put(`/website-versions/${id}`, data);
      } else {
        return await axiosInstance.post('/website-versions/', data);
      }
    },
    onSuccess: () => {
      message.success(isEdit ? '版本更新成功' : '版本创建成功');
      navigate('/site-settings/website-versions');
    },
    onError: (error: any) => {
      message.error(error.response?.data?.detail || '保存失败');
    },
  });

  const handleSave = async (formData: any) => {
    setLoading(true);
    try {
      // 处理日期字段
      let releaseDate = dayjs().toISOString();
      if (formData.release_date) {
        if (dayjs.isDayjs(formData.release_date)) {
          releaseDate = formData.release_date.toISOString();
        } else if (typeof formData.release_date === 'string') {
          releaseDate = dayjs(formData.release_date).toISOString();
        }
      }

      const versionData: WebsiteVersionData = {
        version: formData.version || '',
        title: formData.title || '',
        content: formData.content || '',
        release_date: releaseDate,
        is_published: formData.is_published || false,
        is_major: formData.is_major || false,
        author: formData.author || 'ChenJY',
        tags: formData.tags || '',
      };

      // console.log('保存版本数据:', versionData); // 已清理调试代码
      await saveMutation.mutateAsync(versionData);
    } catch (error) {
      console.error('保存失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/site-settings/website-versions');
  };

  const handlePreview = (data: any) => {
    // console.log('预览数据:', data); // 已清理调试代码
    setPreviewData(data);
    setPreviewVisible(true);
  };

  // 网站版本配置
  const getVersionConfig = (): ContentTypeConfig => ({
    type: 'custom' as any,
    name: '网站版本',
    icon: '🚀',
    description: '创建和编辑网站版本信息',
    fields: [
      {
        name: 'version',
        label: '版本号',
        type: 'input',
        required: true,
        placeholder: '如：v1.0.0',
        rules: [
          { required: true, message: '请输入版本号' },
          { pattern: /^v?\d+\.\d+\.\d+/, message: '版本号格式不正确，如：v1.0.0' }
        ]
      },
      {
        name: 'title',
        label: '版本标题',
        type: 'input',
        required: true,
        placeholder: '如：主要功能更新',
        rules: [{ required: true, message: '请输入版本标题' }]
      },
      {
        name: 'release_date',
        label: '发布日期',
        type: 'date',
        required: true,
        tooltip: '此日期将在前端页面显示，并用于版本排序',
        rules: [{ required: true, message: '请选择发布日期' }]
      },
      {
        name: 'author',
        label: '发布者',
        type: 'input',
        placeholder: '发布者姓名',
        defaultValue: 'ChenJY'
      },
      {
        name: 'tags',
        label: '标签',
        type: 'input',
        placeholder: '多个标签用逗号分隔，如：feature,update,bugfix',
        help: '标签用于分类和筛选版本'
      },
      {
        name: 'is_published',
        label: '发布状态',
        type: 'switch',
        defaultValue: false,
        help: '是否立即发布此版本到前端展示'
      },
      {
        name: 'is_major',
        label: '主要版本',
        type: 'switch',
        defaultValue: false,
        help: '主要版本会在前端有特殊标识显示'
      }
    ],
    editor: {
      label: '版本内容',
      placeholder: '详细描述本次版本的更新内容...',
      height: 1000,
      features: {
        toolbar: true,
        preview: true,
        fullscreen: true,
        imageUpload: true,
        templateSelector: true,
        autoSave: true,
        versionHistory: false
      }
    },
    actions: [
      {
        key: 'save',
        label: '保存',
        type: 'save',
        primary: true,
        icon: 'SaveOutlined'
      },
      {
        key: 'template',
        label: '模板',
        type: 'template',
        icon: 'ThunderboltOutlined'
      },
      {
        key: 'cancel',
        label: '取消',
        type: 'cancel',
        icon: 'CloseOutlined'
      }
    ],
    validation: {
      required: ['version', 'title', 'content', 'release_date']
    },
    api: {
      create: '/website-versions/',
      update: '/website-versions/{id}',
      get: '/website-versions/{id}',
      delete: '/website-versions/{id}',
      list: '/website-versions/',
      templates: '/content-manager/templates?template_type=website_version'
    },
    layout: {
      formSpan: 6,
      editorSpan: 18,
      previewMode: 'tab'
    },
    features: {
      autoSave: true,
      versionHistory: false,
      templates: true,
      preview: true,
      seo: false,
      scheduling: false
    }
  });

  // 转换初始数据格式以适配编辑器
  const getInitialData = () => {
    if (!initialData) return undefined;

    return {
      version: initialData.version,
      title: initialData.title,
      content: initialData.content,
      release_date: initialData.release_date ? dayjs(initialData.release_date) : dayjs(),
      author: initialData.author || 'ChenJY',
      tags: initialData.tags || '',
      is_published: initialData.is_published || false,
      is_major: initialData.is_major || false
    };
  };

  if (isLoadingData && isEdit) {
    return <div>加载中...</div>;
  }

  return (
    <div className="website-version-edit-page">
      <UniversalContentEditor
        contentType="custom"
        contentId={id}
        mode={mode}
        initialData={getInitialData()}
        onSave={handleSave}
        onCancel={handleCancel}
        onPreview={handlePreview}
        loading={loading}
        customConfig={getVersionConfig()}
      />

      {/* 预览模态框 */}
      <Modal
        title="版本预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width={1000}
        footer={null}
        style={{ top: 20 }}
      >
        {previewData && (
          <ContentPreview
            config={getVersionConfig()}
            data={previewData}
            contentType="custom"
          />
        )}
      </Modal>
    </div>
  );
};

export default WebsiteVersionEditPage;
