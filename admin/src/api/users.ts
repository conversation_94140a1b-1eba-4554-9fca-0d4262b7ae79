import axiosInstance from './axiosInstance';
import { UserResponse, UserCreate, UserUpdate } from '../types/user';

// Type for paginated response (if backend supports it)
// Adjust based on actual backend response structure
interface PaginatedUsersResponse {
  items: UserResponse[];
  total: number;
  page: number;
  size: number;
}

// Function to get users with pagination, search, and filter
export const getUsers = async (
  page: number = 1, 
  limit: number = 10, 
  search?: string,
  role?: 'admin' | 'user',
  isActive?: boolean
  // Add other filters as needed
): Promise<PaginatedUsersResponse> => { // Assuming backend returns paginated data
  const params = new URLSearchParams();
  params.append('skip', String((page - 1) * limit));
  params.append('limit', String(limit));
  if (search) {
    params.append('search', search);
  }
  if (role) {
    params.append('role', role);
  }
  if (isActive !== undefined) {
    params.append('is_active', String(isActive));
  }
  
  // console.log(`[API] Fetching users with params: ${params.toString()}`); // 已清理调试代码
  // Adjust endpoint if necessary
  const response = await axiosInstance.get<PaginatedUsersResponse>('/users', { params }); 
  // Simulate pagination if backend doesn't return it yet
  // return { items: response.data, total: response.data.length, page, size: limit }; 
  return response.data; 
};

// Function to get a single user by ID
export const getUserById = async (userId: number): Promise<UserResponse> => {
  const response = await axiosInstance.get<UserResponse>(`/users/${userId}`);
  return response.data;
};

// Function to create a new user
export const createUser = async (userData: UserCreate): Promise<UserResponse> => {
  const response = await axiosInstance.post<UserResponse>('/users', userData);
  return response.data;
};

// Function to update a user
export const updateUser = async (userId: number, userData: UserUpdate): Promise<UserResponse> => {
  const response = await axiosInstance.put<UserResponse>(`/users/${userId}`, userData);
  return response.data;
};

// Function to delete a user
export const deleteUser = async (userId: number): Promise<void> => {
  await axiosInstance.delete(`/users/${userId}`);
}; 