import axiosInstance from './axiosInstance';
import { LoginCredentials, LoginResponse, User } from '../types/auth'; // Assuming User type is defined in auth types

// 登录请求
export const loginUser = async (credentials: LoginCredentials): Promise<LoginResponse> => {
  // const response = await axiosInstance.post<LoginResponse>('/auth/login', credentials);
  // 注意：FastAPI 的 OAuth2PasswordRequestForm 期望的是 form-data 格式
  // 因此，需要将数据格式化为 URLSearchParams 并设置正确的 Content-Type
  const params = new URLSearchParams();
  params.append('username', credentials.username);
  params.append('password', credentials.password);

  const response = await axiosInstance.post<LoginResponse>('/auth/login', params, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
  return response.data;
};

// Function to get current user details (requires auth token in header)
export const getCurrentUser = async (): Promise<User> => {
    // console.log("[getCurrentUser] Function called."); // Log at the very beginning // 已清理调试代码
    try {
        // console.log("[getCurrentUser] Attempting axiosInstance.get('/auth/users/me')..."); // 已清理调试代码
        const response = await axiosInstance.get<User>('/auth/users/me'); // Use the correct endpoint path
        // console.log("[getCurrentUser] API call successful. Fetched user:", response.data); // 已清理调试代码
        return response.data;
    } catch (error) {
        console.error("[getCurrentUser] Error during API call:", error);
        // Re-throw the error so the caller (LoginPage) can catch it
        throw error;
    }
};

// // 获取当前用户信息 (如果后端提供)
// export const getCurrentUser = async (): Promise<User> => {
//   const response = await axiosInstance.get<User>('/users/me'); // 假设有此接口
//   return response.data;
// }; 