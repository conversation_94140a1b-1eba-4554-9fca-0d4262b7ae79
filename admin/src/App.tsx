import React, { useEffect } from 'react';
import { <PERSON>rowserRouter, useRoutes } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { routes } from './routes';
import { GlobalConfirmDialog } from './components/common/ConfirmDialog';
import { useAuthStore } from './store/authStore';
import { getCurrentUser } from './api/auth';
import ErrorBoundary from './components/ErrorBoundary';
import { ErrorProvider, setupGlobalErrorHandling, useErrorContext } from './contexts/ErrorContext';
import './App.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

const AppRoutes: React.FC = () => {
  const element = useRoutes(routes);
  const { token, user, setUser, clearAuth } = useAuthStore();
  const { reportError } = useErrorContext();

  // 在应用启动时，如果有token但没有user信息，自动获取用户信息
  useEffect(() => {
    const initializeUser = async () => {
      if (token && !user) {
        try {
          // console.log('[App] Token exists but no user info, fetching user data...'); // 已清理调试代码
          const userData = await getCurrentUser();
          setUser(userData);
          // console.log('[App] User data fetched successfully:', userData); // 已清理调试代码
        } catch (error) {
          console.error('[App] Failed to fetch user data:', error);
          // 报告错误到全局错误处理系统
          reportError(
            error instanceof Error ? error : new Error('Failed to fetch user data'),
            'USER_INITIALIZATION',
            'high'
          );
          // 如果获取用户信息失败，清除无效的token
          clearAuth();
        }
      }
    };

    initializeUser();
  }, [token, user, setUser, clearAuth, reportError]);

  return element;
};

function App() {
  return (
    <ErrorBoundary>
      <ErrorProvider>
        <QueryClientProvider client={queryClient}>
          <ConfigProvider locale={zhCN}>
            <BrowserRouter>
              <AppWithErrorHandling />
            </BrowserRouter>
            <GlobalConfirmDialog />
          </ConfigProvider>
          <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
      </ErrorProvider>
    </ErrorBoundary>
  );
}

// 包含错误处理设置的应用组件
const AppWithErrorHandling: React.FC = () => {
  const { reportError } = useErrorContext();

  useEffect(() => {
    // 设置全局错误处理
    setupGlobalErrorHandling(reportError);
  }, [reportError]);

  return <AppRoutes />;
};

export default App;
