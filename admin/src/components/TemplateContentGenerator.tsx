import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Space, Typography, Alert, Row, Col, Tag, Pagination, Tabs, Select, Input } from 'antd';
import { FileTextOutlined, ThunderboltOutlined, FolderOutlined, SearchOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import axiosInstance from '../api/axiosInstance';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface TemplateContentGeneratorProps {
  visible: boolean;
  templates: any[];
  onCancel: () => void;
  onApply: (templateContent: string) => void;
  loading?: boolean;
  contentType?: 'blog' | 'project' | 'timeline' | 'about' | 'custom'; // 添加内容类型
}

const TemplateContentGenerator: React.FC<TemplateContentGeneratorProps> = ({
  visible,
  templates,
  onCancel,
  onApply,
  loading = false,
  contentType = 'blog'
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const pageSize = 6; // 每页显示6个模板

  // 获取模板分类
  const { data: categories = [], isLoading: categoriesLoading } = useQuery({
    queryKey: ['template-categories'],
    queryFn: async () => {
      const response = await axiosInstance.get('/template-categories', {
        params: { include_universal: true }
      });
      return response.data;
    },
    enabled: visible
  });

  // 重置选择当模态框关闭时
  useEffect(() => {
    if (!visible) {
      setSelectedTemplate(null);
      setCurrentPage(1);
      setSelectedCategory('all');
      setSearchKeyword('');
    }
  }, [visible]);

  // 过滤模板
  const filteredTemplates = templates.filter(template => {
    // 分类过滤
    if (selectedCategory !== 'all') {
      const templateCategories = template.categories || [];
      const hasCategory = templateCategories.some((cat: any) => cat.name === selectedCategory);
      if (!hasCategory) return false;
    }

    // 关键词搜索
    if (searchKeyword) {
      const keyword = searchKeyword.toLowerCase();
      return template.name.toLowerCase().includes(keyword) ||
             (template.description && template.description.toLowerCase().includes(keyword));
    }

    return true;
  });

  // 计算当前页的模板
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentTemplates = filteredTemplates.slice(startIndex, endIndex);

  // 生成模板内容结构
  const generateTemplateContent = (template: any): string => {
    if (!template?.content_template) {
      console.error('模板缺少 content_template 字段:', template);
      return '';
    }

    const content = template.content_template;
    const fieldsConfig = template.fields_config || {};

    // console.log('生成模板内容:', { template: template.name, content, fieldsConfig }); // 已清理调试代码

    // 解析模板内容，为每个变量创建输入提示
    let processedContent = content;

    // 找到所有变量
    const variables = content.match(/\{\{(\w+)\}\}/g) || [];
    const uniqueVariables = [...new Set(variables)];

    // console.log('找到的变量:', uniqueVariables); // 已清理调试代码

    // 为每个变量创建输入区域
    uniqueVariables.forEach(variable => {
      const varName = variable.replace(/[{}]/g, '');
      const fieldConfig = fieldsConfig[varName] || {};
      const label = fieldConfig.label || varName;
      const isRequired = fieldConfig.required ? ' *' : '';

      let placeholder = '';
      if (fieldConfig.type === 'textarea') {
        placeholder = `[请在此处输入${label}${isRequired}]\n\n`;
      } else if (fieldConfig.type === 'code') {
        placeholder = `[请在此处输入${label}${isRequired}]\n// 在这里写代码\n\n`;
      } else if (fieldConfig.type === 'select' && fieldConfig.options) {
        placeholder = `[请选择${label}${isRequired}: ${fieldConfig.options.join(' | ')}]`;
      } else {
        placeholder = `[请在此处输入${label}${isRequired}]`;
      }

      // 使用更安全的替换方法
      // variable 是 "{{varName}}" 格式，需要转义大括号
      const escapedVariable = variable.replace(/[{}]/g, '\\$&');
      const regex = new RegExp(escapedVariable, 'g');
      const beforeReplace = processedContent;
      processedContent = processedContent.replace(regex, placeholder);

      // console.log(`替换变量 ${variable} -> ${placeholder}`); // 已清理调试代码
      // console.log(`替换前包含变量: ${beforeReplace.includes(variable)}`); // 已清理调试代码
      // console.log(`替换后包含变量: ${processedContent.includes(variable)}`); // 已清理调试代码
    });

    // 添加模板使用说明
    const instructions = `<!--
📝 模板使用说明：
- 这是基于"${template.name}"模板生成的内容结构
- 请在 [请在此处输入...] 标记的位置填写相应内容
- 带 * 号的字段为必填项
- 填写完成后可以删除这些说明和占位符
- 支持 Markdown 格式
-->

`;

    const finalContent = instructions + processedContent;
    // console.log('最终生成的内容:', finalContent); // 已清理调试代码

    return finalContent;
  };

  // 应用模板
  const handleApplyTemplate = () => {
    if (!selectedTemplate) {
      console.error('没有选择模板');
      return;
    }

    // console.log('开始应用模板:', selectedTemplate); // 已清理调试代码
    const templateContent = generateTemplateContent(selectedTemplate);
    // console.log('调用 onApply，传递内容:', templateContent); // 已清理调试代码
    onApply(templateContent);
  };

  return (
    <Modal
      title={
        <Space>
          <FileTextOutlined />
          选择{contentType === 'blog' ? '博客' : contentType === 'project' ? '项目' : contentType === 'about' ? '关于页面' : contentType === 'custom' ? '网站版本' : '时间线'}模板
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button 
          key="apply" 
          type="primary" 
          icon={<ThunderboltOutlined />}
          disabled={!selectedTemplate}
          loading={loading}
          onClick={handleApplyTemplate}
        >
          应用模板到编辑器
        </Button>
      ]}
    >
      {/* 搜索和过滤区域 */}
      <div style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Input
              placeholder="搜索模板..."
              prefix={<SearchOutlined />}
              value={searchKeyword}
              onChange={(e) => {
                setSearchKeyword(e.target.value);
                setCurrentPage(1); // 重置页码
              }}
              allowClear
            />
          </Col>
          <Col span={12}>
            <Select
              style={{ width: '100%' }}
              placeholder="选择分类"
              value={selectedCategory}
              onChange={(value) => {
                setSelectedCategory(value);
                setCurrentPage(1); // 重置页码
              }}
              loading={categoriesLoading}
            >
              <Select.Option value="all">
                <Space>
                  <FolderOutlined />
                  全部分类
                </Space>
              </Select.Option>
              {categories.map((category: any) => (
                <Select.Option key={category.id} value={category.name}>
                  <Space>
                    <span style={{ color: category.color }}>{category.icon}</span>
                    {category.display_name}
                    <Tag size="small">{category.template_count}</Tag>
                  </Space>
                </Select.Option>
              ))}
            </Select>
          </Col>
        </Row>
      </div>

      <div style={{ marginBottom: 16 }}>
        <Alert
          message="选择模板后，将在内容编辑器中生成对应的分块结构"
          description="你可以直接在生成的结构中填写内容，所有占位符都会标明需要填写的内容类型"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      </div>

      {filteredTemplates && filteredTemplates.length > 0 ? (
        <>
          <Row gutter={[16, 16]}>
            {currentTemplates.map((template: any) => (
            <Col span={24} key={template.id}>
              <Card
                hoverable
                onClick={() => setSelectedTemplate(template)}
                style={{
                  border: selectedTemplate?.id === template.id ? '2px solid #1890ff' : '1px solid #d9d9d9',
                  cursor: 'pointer'
                }}
                bodyStyle={{ padding: '16px' }}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <div style={{ flex: 1 }}>
                    <Title level={5} style={{ margin: '0 0 8px 0' }}>
                      <Space>
                        <FileTextOutlined />
                        {template.name}
                        {template.is_default && (
                          <Tag color="green">默认</Tag>
                        )}
                      </Space>
                    </Title>
                    
                    <Paragraph style={{ margin: '0 0 12px 0', color: '#666' }}>
                      {template.description}
                    </Paragraph>
                    
                    <div style={{ display: 'flex', gap: '8px', alignItems: 'center', marginBottom: '8px', flexWrap: 'wrap' }}>
                      {template.categories && template.categories.length > 0 ? (
                        template.categories.map((category: any) => (
                          <Tag
                            key={category.id}
                            color={category.color}
                            style={{ margin: '2px' }}
                          >
                            <Space size={4}>
                              <span>{category.icon}</span>
                              {category.display_name}
                            </Space>
                          </Tag>
                        ))
                      ) : (
                        <Tag>{template.category}</Tag>
                      )}
                    </div>
                    
                    {template.tags && template.tags.length > 0 && (
                      <div>
                        {template.tags.map((tag: string) => (
                          <Tag key={tag} style={{ marginBottom: '4px' }}>
                            {tag}
                          </Tag>
                        ))}
                      </div>
                    )}
                  </div>
                  
                  {selectedTemplate?.id === template.id && (
                    <div style={{ marginLeft: '16px' }}>
                      <Tag color="blue">已选择</Tag>
                    </div>
                  )}
                </div>
              </Card>
            </Col>
            ))}
          </Row>

          {/* 分页组件 */}
          {filteredTemplates.length > pageSize && (
            <div style={{ textAlign: 'center', marginTop: '24px' }}>
              <Pagination
                current={currentPage}
                total={filteredTemplates.length}
                pageSize={pageSize}
                onChange={setCurrentPage}
                showSizeChanger={false}
                showQuickJumper={false}
                showTotal={(total, range) =>
                  `第 ${range[0]}-${range[1]} 项，共 ${total} 个模板`
                }
              />
            </div>
          )}
        </>
      ) : (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Text type="secondary">暂无可用模板</Text>
        </div>
      )}
    </Modal>
  );
};

export default TemplateContentGenerator;
