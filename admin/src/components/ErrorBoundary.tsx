import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button, Typography, Card, Collapse, Space } from 'antd';
import { 
  BugOutlined, 
  ReloadOutlined, 
  HomeOutlined, 
  WarningOutlined,
  InfoCircleOutlined 
} from '@ant-design/icons';
import { log } from '../utils/logger';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    this.setState({
      error,
      errorInfo
    });

    // 记录到日志系统
    log.error('React Error Boundary caught an error', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      errorInfo: {
        componentStack: errorInfo.componentStack
      },
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }, 'ERROR_BOUNDARY');

    // 调用外部错误处理函数
    this.props.onError?.(error, errorInfo);

    // 发送错误报告到服务器（可选）
    this.sendErrorReport(error, errorInfo);
  }

  sendErrorReport = async (error: Error, errorInfo: ErrorInfo) => {
    try {
      // 这里可以发送错误报告到服务器
      const errorReport = {
        errorId: this.state.errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: localStorage.getItem('userId') || 'anonymous'
      };

      // 发送到错误收集服务
      // await fetch('/api/errors/report', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport)
      // });

      // console.log('Error report prepared:', errorReport); // 已清理调试代码
    } catch (reportError) {
      console.error('Failed to send error report:', reportError);
    }
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error, errorInfo, errorId } = this.state;

      return (
        <div style={{ padding: '50px 20px', minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
          <Card style={{ maxWidth: 800, margin: '0 auto' }}>
            <Result
              status="error"
              icon={<BugOutlined style={{ color: '#ff4d4f' }} />}
              title="页面出现了错误"
              subTitle={
                <Space direction="vertical" size="small">
                  <Text type="secondary">
                    抱歉，页面遇到了意外错误。我们已经记录了这个问题，请尝试以下解决方案：
                  </Text>
                  <Text code style={{ fontSize: '12px' }}>
                    错误ID: {errorId}
                  </Text>
                </Space>
              }
              extra={
                <Space wrap>
                  <Button type="primary" icon={<ReloadOutlined />} onClick={this.handleRetry}>
                    重试
                  </Button>
                  <Button icon={<ReloadOutlined />} onClick={this.handleReload}>
                    刷新页面
                  </Button>
                  <Button icon={<HomeOutlined />} onClick={this.handleGoHome}>
                    返回首页
                  </Button>
                </Space>
              }
            />

            {/* 开发环境显示详细错误信息 */}
            {process.env.NODE_ENV === 'development' && error && (
              <Card 
                title={
                  <Space>
                    <WarningOutlined style={{ color: '#faad14' }} />
                    开发调试信息
                  </Space>
                }
                style={{ marginTop: 20 }}
                size="small"
              >
                <Collapse size="small">
                  <Panel 
                    header={
                      <Space>
                        <InfoCircleOutlined />
                        错误详情
                      </Space>
                    } 
                    key="error"
                  >
                    <div style={{ marginBottom: 16 }}>
                      <Text strong>错误类型: </Text>
                      <Text code>{error.name}</Text>
                    </div>
                    <div style={{ marginBottom: 16 }}>
                      <Text strong>错误消息: </Text>
                      <Paragraph copyable>
                        <Text code>{error.message}</Text>
                      </Paragraph>
                    </div>
                    {error.stack && (
                      <div style={{ marginBottom: 16 }}>
                        <Text strong>错误堆栈: </Text>
                        <Paragraph copyable>
                          <pre style={{ 
                            fontSize: '12px', 
                            backgroundColor: '#f5f5f5', 
                            padding: '8px',
                            borderRadius: '4px',
                            overflow: 'auto',
                            maxHeight: '200px'
                          }}>
                            {error.stack}
                          </pre>
                        </Paragraph>
                      </div>
                    )}
                  </Panel>
                  
                  {errorInfo && (
                    <Panel 
                      header={
                        <Space>
                          <InfoCircleOutlined />
                          组件堆栈
                        </Space>
                      } 
                      key="component"
                    >
                      <Paragraph copyable>
                        <pre style={{ 
                          fontSize: '12px', 
                          backgroundColor: '#f5f5f5', 
                          padding: '8px',
                          borderRadius: '4px',
                          overflow: 'auto',
                          maxHeight: '200px'
                        }}>
                          {errorInfo.componentStack}
                        </pre>
                      </Paragraph>
                    </Panel>
                  )}
                </Collapse>
              </Card>
            )}
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
