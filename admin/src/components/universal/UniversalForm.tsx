import React, { useState, useEffect } from 'react';
import {
  Form, Input, Select, DatePicker, Switch, InputNumber,
  Collapse, Space, Tag, Button, Upload, message
} from 'antd';
import {
  PlusOutlined, DeleteOutlined, UploadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { ContentTypeConfig, FieldConfig } from '../../types/universal';
import BlogImageGrid from '../blog/BlogImageGrid';
import IconField from '../icons/IconField';
import axiosInstance from '../../api/axiosInstance';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Option } = Select;
const { Panel } = Collapse;

interface UniversalFormProps {
  config: ContentTypeConfig;
  contentType: string;
  mode: 'create' | 'edit';
  initialData?: any;
}

const UniversalForm: React.FC<UniversalFormProps> = ({
  config,
  contentType,
  mode,
  initialData
}) => {
  const [tags, setTags] = useState<any[]>([]);
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [currentImageField, setCurrentImageField] = useState<string>('');

  // 加载标签数据
  useEffect(() => {
    if (config.fields.some(field => field.type === 'tags')) {
      // 这里可以根据内容类型加载不同的标签
      // 暂时使用博客标签作为示例
      fetchTags();
    }
  }, [config]);

  const fetchTags = async () => {
    try {
      // 从真实的标签API获取数据
      const response = await axiosInstance.get('/tags/');
      // console.log('获取到的标签数据:', response.data); // 已清理调试代码
      setTags(response.data);
    } catch (error) {
      console.error('加载标签失败:', error);
      // 如果API失败，使用空数组
      setTags([]);
    }
  };

  // 渲染字段
  const renderField = (field: FieldConfig) => {
    const commonProps = {
      placeholder: field.placeholder,
      disabled: false // 可以根据权限设置
    };

    switch (field.type) {
      case 'input':
        return <Input {...commonProps} />;
        
      case 'textarea':
        return (
          <TextArea 
            {...commonProps} 
            rows={4}
            showCount
            maxLength={field.name === 'description' ? 500 : undefined}
          />
        );

      case 'select':
        return (
          <Select
            {...commonProps}
            allowClear={!field.required}
            disabled={field.name === 'article_type' && mode === 'edit'} // 编辑时禁用文章类型修改
          >
            {field.options?.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );

      case 'date':
        return (
          <DatePicker
            {...commonProps}
            showTime
            format="YYYY-MM-DD HH:mm"
            style={{ width: '100%' }}
            getPopupContainer={(trigger) => trigger.parentElement || document.body}
            defaultValue={field.name === 'display_date' || field.name === 'date' ? dayjs() : undefined}
            placeholder={field.name === 'updated_date' ? '选择修改日期（可选）' : undefined}
            showNow={false}
          />
        );
        
      case 'switch':
        return (
          <Switch 
            checkedChildren="是" 
            unCheckedChildren="否"
            defaultChecked={field.defaultValue}
          />
        );
        
      case 'number':
        return <InputNumber {...commonProps} style={{ width: '100%' }} />;

      case 'tags':
        // 根据内容类型过滤标签
        const getFilteredTags = () => {
          if (contentType === 'project') {
            // 项目只显示技术标签和内容标签，不显示类型标签
            return tags.filter(tag => tag.category !== 'type');
          } else if (contentType === 'blog') {
            // 博客显示内容标签，不显示类型标签
            return tags.filter(tag => tag.category !== 'type');
          }
          return tags;
        };

        const filteredTags = getFilteredTags();

        return (
          <Select
            mode="multiple"
            placeholder={field.placeholder || '选择标签'}
            style={{ width: '100%' }}
            optionLabelProp="label"
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          >
            {filteredTags.map(tag => (
              <Option key={tag.id} value={tag.id} label={tag.name}>
                <Tag color={tag.color}>{tag.name}</Tag>
              </Option>
            ))}
          </Select>
        );
        
      case 'image':
        return (
          <div>
            <Button
              icon={<UploadOutlined />}
              onClick={() => {
                setCurrentImageField(field.name);
                setImageModalVisible(true);
              }}
            >
              选择图片
            </Button>
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              {field.name === 'image_ids' ? '可选择多张图片' : '选择单张图片'}
            </div>
          </div>
        );
        
      case 'url':
        return (
          <Input
            {...commonProps}
            addonBefore="https://"
            placeholder="请输入URL"
          />
        );

      case 'icon':
        return (
          <IconField
            placeholder={field.placeholder || '选择图标'}
            size={24}
            theme="light"
            allowClear={!field.required}
          />
        );

      default:
        return <Input {...commonProps} />;
    }
  };

  // 按组分组字段
  const groupedFields = config.fields.reduce((groups, field) => {
    const group = field.group || 'basic';
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(field);
    return groups;
  }, {} as Record<string, FieldConfig[]>);

  // 处理图片选择
  const handleImageSelect = (image: any) => {
    // 这里需要根据字段类型处理图片选择
    // console.log('选择图片:', image, '字段:', currentImageField); // 已清理调试代码
    setImageModalVisible(false);
  };

  return (
    <div className="universal-form">
      {Object.keys(groupedFields).length > 1 ? (
        // 如果有多个分组，使用折叠面板
        <Collapse
          defaultActiveKey={['basic']}
          ghost
          items={Object.entries(groupedFields).map(([groupName, fields]) => ({
            key: groupName,
            label: (
              <Space>
                <InfoCircleOutlined />
                {getGroupLabel(groupName)}
              </Space>
            ),
            children: (
              <>
                {fields.map(field => (
                  <Form.Item
                    key={field.name}
                    name={field.name}
                    label={field.label}
                    rules={field.rules}
                    tooltip={field.tooltip}
                    valuePropName={field.type === 'switch' ? 'checked' : 'value'}
                    initialValue={field.type === 'date' && field.defaultValue ? dayjs(field.defaultValue) : field.defaultValue}
                  >
                    {renderField(field)}
                  </Form.Item>
                ))}

                {/* 在基本信息分组中添加时间信息显示 */}
                {groupName === 'basic' && mode === 'edit' && initialData && (
                  <>
                    {initialData.published_at && (
                      <Form.Item label="发布时间">
                        <Input
                          value={dayjs(initialData.published_at).format('YYYY-MM-DD HH:mm:ss')}
                          disabled
                          style={{ color: '#666' }}
                        />
                      </Form.Item>
                    )}
                    {initialData.updated_at && (
                      <Form.Item label="最后修改时间">
                        <Input
                          value={dayjs(initialData.updated_at).format('YYYY-MM-DD HH:mm:ss')}
                          disabled
                          style={{ color: '#666' }}
                        />
                      </Form.Item>
                    )}
                  </>
                )}
              </>
            )
          }))}
        />
      ) : (
        // 如果只有一个分组，直接显示字段
        <>
          {config.fields.map(field => (
            <Form.Item
              key={field.name}
              name={field.name}
              label={field.label}
              rules={field.rules}
              tooltip={field.tooltip}
              valuePropName={field.type === 'switch' ? 'checked' : 'value'}
              initialValue={field.type === 'date' && field.defaultValue ? dayjs(field.defaultValue) : field.defaultValue}
            >
              {renderField(field)}
            </Form.Item>
          ))}

          {/* 在编辑模式下添加时间信息显示 */}
          {mode === 'edit' && initialData && (
            <>
              {initialData.published_at && (
                <Form.Item label="发布时间">
                  <Input
                    value={dayjs(initialData.published_at).format('YYYY-MM-DD HH:mm:ss')}
                    disabled
                    style={{ color: '#666' }}
                  />
                </Form.Item>
              )}
              {initialData.updated_at && (
                <Form.Item label="最后修改时间">
                  <Input
                    value={dayjs(initialData.updated_at).format('YYYY-MM-DD HH:mm:ss')}
                    disabled
                    style={{ color: '#666' }}
                  />
                </Form.Item>
              )}
            </>
          )}
        </>
      )}

      {/* 图片选择模态框 */}
      {imageModalVisible && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '20px',
            borderRadius: '8px',
            width: '80%',
            maxWidth: '1000px',
            maxHeight: '80%',
            overflow: 'auto'
          }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '16px'
            }}>
              <h3>选择图片</h3>
              <Button onClick={() => setImageModalVisible(false)}>
                关闭
              </Button>
            </div>
            <BlogImageGrid onSelect={handleImageSelect} />
          </div>
        </div>
      )}
    </div>
  );
};

// 获取分组标签
function getGroupLabel(groupName: string): string {
  const labels: Record<string, string> = {
    basic: '基本信息',
    seo: 'SEO设置',
    advanced: '高级设置',
    social: '社交媒体',
    technical: '技术信息'
  };
  return labels[groupName] || groupName;
}

export default UniversalForm;
