import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Card, Form, Button, Space, Tabs, Alert, Spin, message,
  Row, Col, Typography, Divider
} from 'antd';
import {
  SaveOutlined, EyeOutlined, SettingOutlined, 
  HistoryOutlined, ThunderboltOutlined
} from '@ant-design/icons';
import EnhancedMarkdownEditor from '../EnhancedMarkdownEditor';
import TemplateContentGenerator from '../TemplateContentGenerator';
import BlogVersionHistory from '../BlogVersionHistory';
import { ContentTypeConfig, ContentData, ContentFormData } from '../../types/universal';
import { getContentTypeConfig } from '../../config/contentTypes';
import UniversalForm from './UniversalForm';
import ContentPreview from './ContentPreview';
import ContentActions from './ContentActions';
import axiosInstance from '../../api/axiosInstance';
import { useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';

const { Title } = Typography;
const { TabPane } = Tabs;

interface UniversalContentEditorProps {
  contentType: 'blog' | 'project' | 'timeline' | 'about' | 'custom';
  contentId?: string | number; // 编辑模式时的内容ID
  initialData?: Partial<ContentData>; // 初始数据
  onSave: (data: ContentFormData) => Promise<void>;
  onCancel?: () => void;
  onPreview?: (data: ContentFormData) => void;
  loading?: boolean;
  mode?: 'create' | 'edit'; // 创建或编辑模式
  customConfig?: Partial<ContentTypeConfig>; // 自定义配置覆盖
}

const UniversalContentEditor: React.FC<UniversalContentEditorProps> = ({
  contentType,
  contentId,
  initialData,
  onSave,
  onCancel,
  onPreview,
  loading = false,
  mode = 'create',
  customConfig
}) => {
  const [form] = Form.useForm();
  const [content, setContent] = useState('');
  const [activeTab, setActiveTab] = useState('edit');
  const [saving, setSaving] = useState(false);
  const [config, setConfig] = useState<ContentTypeConfig | null>(null);
  const [formData, setFormData] = useState<Partial<ContentFormData>>({});
  const [editorHeight, setEditorHeight] = useState(800); // 增加默认高度
  const formContainerRef = useRef<HTMLDivElement>(null);

  // 模板和版本功能状态
  const [templateGeneratorVisible, setTemplateGeneratorVisible] = useState(false);
  const [versionHistoryVisible, setVersionHistoryVisible] = useState(false);
  const [editorKey, setEditorKey] = useState(0); // 用于强制重新渲染编辑器

  // 获取模板数据 - 根据内容类型获取对应模板
  const { data: templates, isLoading: isLoadingTemplates } = useQuery({
    queryKey: ['templates', contentType, customConfig?.api?.templates],
    queryFn: async () => {
      try {
        // 如果有自定义模板API，使用自定义API
        if (customConfig?.api?.templates) {
          const response = await axiosInstance.get(customConfig.api.templates);
          return response.data;
        }

        // 否则使用通用的内容模板API，根据内容类型过滤
        const templateType = contentType === 'custom' ? 'website_version' : contentType;
        const response = await axiosInstance.get('/content-manager/templates', {
          params: {
            is_active: true,
            template_type: templateType
          }
        });
        return response.data;
      } catch (error) {
        // console.log('获取模板失败:', error); // 已清理调试代码
        return [];
      }
    },
    staleTime: 10 * 60 * 1000 // Cache templates for 10 minutes
  });

  // 获取内容类型配置
  useEffect(() => {
    const typeConfig = getContentTypeConfig(contentType);
    if (typeConfig) {
      // 合并自定义配置
      const mergedConfig = customConfig
        ? { ...typeConfig, ...customConfig }
        : typeConfig;
      setConfig(mergedConfig);
    } else if (customConfig) {
      // 如果没有预定义配置，直接使用自定义配置
      setConfig(customConfig as ContentTypeConfig);
    }
  }, [contentType, customConfig]);

  // 监听表单高度变化，调整编辑器高度
  useEffect(() => {
    const updateEditorHeight = () => {
      if (formContainerRef.current) {
        const formHeight = formContainerRef.current.offsetHeight;
        // 设置编辑器高度至少与表单高度相同，但不少于800px
        const newHeight = Math.max(formHeight + 100, 800); // 增加100px缓冲，最小800px
        setEditorHeight(newHeight);
        // console.log('表单高度:', formHeight, '编辑器高度:', newHeight); // 已清理调试代码
      }
    };

    // 延迟执行，确保DOM已渲染
    const timer = setTimeout(() => {
      updateEditorHeight();
    }, 100);

    // 监听窗口大小变化
    window.addEventListener('resize', updateEditorHeight);

    // 使用MutationObserver监听DOM变化（表单字段的显示/隐藏）
    const observer = new MutationObserver(() => {
      setTimeout(updateEditorHeight, 50); // 延迟执行
    });

    if (formContainerRef.current) {
      observer.observe(formContainerRef.current, {
        childList: true,
        subtree: true,
        attributes: true
      });
    }

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', updateEditorHeight);
      observer.disconnect();
    };
  }, [config]); // 当配置变化时重新设置

  // 初始化数据
  useEffect(() => {
    if (initialData) {
      // 处理日期字段，确保格式正确
      const processedData = { ...initialData };

      // 处理展示日期字段，修复时区问题
      if (processedData.display_date) {
        if (typeof processedData.display_date === 'string') {
          processedData.display_date = dayjs(processedData.display_date);
        } else if (processedData.display_date instanceof Date) {
          processedData.display_date = dayjs(processedData.display_date);
        }
      }

      // 兼容旧的date字段
      if (processedData.date && !processedData.display_date) {
        if (typeof processedData.date === 'string') {
          processedData.display_date = dayjs(processedData.date);
        } else if (processedData.date instanceof Date) {
          processedData.display_date = dayjs(processedData.date);
        }
      }

      form.setFieldsValue(processedData);
      setContent(processedData.content || '');
      setFormData(processedData);
    }
  }, [initialData, form]);

  // 处理表单值变化
  const handleFormChange = useCallback((changedValues: any, allValues: any) => {
    setFormData({ ...allValues, content });
  }, [content]);

  // 处理内容变化
  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent);
    setFormData(prev => ({ ...prev, content: newContent }));
  }, []);

  // 保存内容
  const handleSave = async () => {
    try {
      setSaving(true);

      // 验证表单
      const values = await form.validateFields();

      // 处理日期字段
      const processedValues = { ...values };

      // 处理展示日期
      if (processedValues.display_date) {
        processedValues.display_date = processedValues.display_date.format('YYYY-MM-DDTHH:mm:ss');
      }

      // 兼容旧的date字段
      if (processedValues.date && !processedValues.display_date) {
        processedValues.display_date = processedValues.date.format('YYYY-MM-DDTHH:mm:ss');
      }

      // 删除旧的date字段，避免冲突
      if (processedValues.date) {
        delete processedValues.date;
      }

      // 合并表单数据和内容
      const saveData: ContentFormData = {
        ...processedValues,
        content,
        contentType,
        mode
      };

      await onSave(saveData);

      // 保存成功后立即重置状态
      setSaving(false);

    } catch (error: any) {
      console.error('保存失败:', error);
      setSaving(false);

      if (error.errorFields) {
        message.error('请检查表单填写是否正确');
      } else {
        message.error(error.message || '保存失败，请稍后重试');
      }
    }
  };

  // 预览内容
  const handlePreview = () => {
    if (onPreview) {
      const previewData: ContentFormData = {
        ...form.getFieldsValue(),
        content,
        contentType,
        mode
      };
      onPreview(previewData);
    }
    setActiveTab('preview');
  };

  // 处理模板选择
  const handleTemplate = () => {
    setTemplateGeneratorVisible(true);
  };



  // 处理版本历史
  const handleHistory = () => {
    if ((contentType === 'blog' || contentType === 'project') && contentId) {
      setVersionHistoryVisible(true);
    } else if (!contentId) {
      message.info('请先保存文章后再查看版本历史');
    } else {
      message.info('版本历史功能暂时只支持博客和项目类型');
    }
  };

  // 应用模板内容到编辑器
  const handleTemplateContentApply = (templateContent: string) => {
    // console.log('应用模板内容到编辑器:', templateContent); // 已清理调试代码

    // 替换当前内容
    setContent(templateContent);
    message.success('模板应用成功！');

    // 强制重新渲染编辑器
    setEditorKey(prev => prev + 1);

    // 关闭模板选择器
    setTemplateGeneratorVisible(false);
  };

  // 版本历史更新回调
  const handleBlogUpdated = async () => {
    if (!contentId) return;

    try {
      // 重新获取最新的文章数据
      const response = await axiosInstance.get(`/blogs/${contentId}`);
      const updatedArticle = response.data;

      // 更新编辑器内容
      setContent(updatedArticle.content || '');
      setEditorKey(prev => prev + 1); // 强制重新渲染编辑器

      // 更新表单数据
      if (updatedArticle) {
        const newFormData = {
          title: updatedArticle.title,
          description: updatedArticle.description,
          author: updatedArticle.author,
          display_date: updatedArticle.display_date,
          published: updatedArticle.published,
          show_on_homepage: updatedArticle.show_on_homepage,
          article_type: updatedArticle.article_type,
          tag_ids: updatedArticle.tags?.map((tag: any) => tag.id) || [],
          // 项目特有字段
          project_status: updatedArticle.project_status,
          featured: updatedArticle.featured,
          display_order: updatedArticle.display_order,
          tech_stack: updatedArticle.tech_stack,
          project_url: updatedArticle.project_url,
          github_url: updatedArticle.github_url,
          // SEO字段
          meta_title: updatedArticle.meta_title,
          meta_description: updatedArticle.meta_description,
          meta_keywords: updatedArticle.meta_keywords,
          og_title: updatedArticle.og_title,
          og_description: updatedArticle.og_description,
          og_image: updatedArticle.og_image,
          canonical_url: updatedArticle.canonical_url
        };

        setFormData(newFormData);

        // 通知父组件数据已更新
        if (onDataChange) {
          onDataChange(newFormData);
        }
      }

      message.success('版本恢复成功！内容已更新到编辑器');
      setVersionHistoryVisible(false);

    } catch (error) {
      console.error('恢复版本失败:', error);
      message.error('恢复版本失败，请重试');
    }
  };

  // 自动保存
  const handleAutoSave = useCallback(async (autoSaveContent: string) => {
    if (!config?.features?.autoSave) return;
    
    try {
      const autoSaveData = {
        ...form.getFieldsValue(),
        content: autoSaveContent,
        contentType,
        timestamp: new Date().toISOString()
      };
      
      // 保存到localStorage
      const key = `autosave-${contentType}-${contentId || 'new'}`;
      localStorage.setItem(key, JSON.stringify(autoSaveData));
      
      // console.log(`自动保存 ${config.name}:`, autoSaveContent.length, '字符'); // 已清理调试代码
    } catch (error) {
      console.error('自动保存失败:', error);
    }
  }, [config, contentType, contentId, form]);

  if (!config) {
    return <Spin tip="加载配置中..." />;
  }

  return (
    <div className="universal-content-editor">
      <Card
        title={
          <Space>
            {config.icon && <span>{config.icon}</span>}
            <Title level={4} style={{ margin: 0 }}>
              {mode === 'create' ? `创建${config.name}` : `编辑${config.name}`}
            </Title>
          </Space>
        }
        extra={
          <ContentActions
            config={config}
            onSave={handleSave}
            onCancel={onCancel}
            onPreview={handlePreview}
            onTemplate={handleTemplate}
            onHistory={handleHistory}
            saving={saving}
            contentType={contentType}
            contentId={contentId}
          />
        }
      >
        <Spin spinning={saving}>
          <Form
            form={form}
            layout="vertical"
            onValuesChange={handleFormChange}
            initialValues={initialData}
          >
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={[
                {
                  key: 'edit',
                  label: (
                    <Space>
                      <SettingOutlined />
                      编辑
                    </Space>
                  ),
                  children: (
                    <Row gutter={24} style={{ alignItems: 'flex-start' }}>
                      {/* 左侧：表单字段 */}
                      <Col span={config.layout?.formSpan || 8}>
                        <div ref={formContainerRef} style={{ paddingBottom: '24px' }}>
                          <UniversalForm
                            config={config}
                            contentType={contentType}
                            mode={mode}
                            initialData={initialData}
                          />
                        </div>
                      </Col>

                      {/* 右侧：内容编辑器 */}
                      <Col span={config.layout?.editorSpan || 16}>
                        <div style={{ marginBottom: 24 }}>
                          <label style={{
                            display: 'block',
                            marginBottom: 8,
                            fontWeight: 'bold'
                          }}>
                            {config.editor.label || '内容'}
                            <span style={{ color: '#ff4d4f' }}>*</span>
                          </label>

                          <div style={{
                            height: `${Math.max(editorHeight, config.editor.height || 1000)}px`,
                            minHeight: '1000px'
                          }}>
                            <EnhancedMarkdownEditor
                              key={editorKey}
                              value={content}
                              onChange={handleContentChange}
                              height={Math.max(editorHeight, config.editor.height || 1000)}
                              placeholder={config.editor.placeholder}
                              autoSaveKey={`${contentType}-${contentId || 'new'}`}
                              onAutoSave={handleAutoSave}
                            />
                          </div>
                        </div>
                      </Col>
                    </Row>
                  )
                },
                // 只有当配置中启用预览功能时才显示预览标签页
                ...(config.features?.preview ? [{
                  key: 'preview',
                  label: (
                    <Space>
                      <EyeOutlined />
                      预览
                    </Space>
                  ),
                  children: (
                    <ContentPreview
                      config={config}
                      data={{ ...formData, content }}
                      contentType={contentType}
                    />
                  )
                }] : [])
              ]}
            />
          </Form>
        </Spin>
      </Card>

      {/* 模板选择器 */}
      <TemplateContentGenerator
        visible={templateGeneratorVisible}
        templates={templates || []}
        onCancel={() => setTemplateGeneratorVisible(false)}
        onApply={handleTemplateContentApply}
        loading={isLoadingTemplates}
        contentType={contentType as 'blog' | 'project' | 'about' | 'custom'}
      />

      {/* 版本历史 */}
      {(contentType === 'blog' || contentType === 'project') && contentId && (
        <BlogVersionHistory
          visible={versionHistoryVisible}
          onClose={() => setVersionHistoryVisible(false)}
          blogSlug={contentId as string}
          blogTitle={formData.title || ''}
          onBlogUpdated={handleBlogUpdated}
          currentContent={content}
          currentFormData={formData}
        />
      )}
    </div>
  );
};

export default UniversalContentEditor;
