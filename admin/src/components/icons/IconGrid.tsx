/**
 * 图标网格组件
 */

import React, { useState } from 'react';
import { Card, Tag, Button, Tooltip, Badge, Spin } from 'antd';
import { HeartOutlined, HeartFilled, EyeOutlined } from '@ant-design/icons';
import { IconGridProps, IconMetadata } from '../../types/icon';
import UniversalIconRenderer from './UniversalIconRenderer';
import { IconApiService } from '../../api/iconApi';
import './IconGrid.css';

const IconGrid: React.FC<IconGridProps> = ({
  icons,
  onSelect,
  selectedIcons,
  viewMode,
  iconSize,
  loading = false,
  onPreview
}) => {
  const [favoriteLoading, setFavoriteLoading] = useState<Record<number, boolean>>({});
  const [renderStates, setRenderStates] = useState<Record<string, {
    loading: boolean;
    error: boolean;
    verified: boolean;
  }>>({});

  const isSelected = (icon: IconMetadata) => {
    return selectedIcons.some(selected => selected.id === icon.id);
  };

  const getLibraryColor = (libraryName: string) => {
    const colors: Record<string, string> = {
      'lucide': '#3b82f6',
      'iconic': '#10b981',
      'iconify': '#8b5cf6'
    };
    return colors[libraryName] || '#6b7280';
  };

  const handleToggleFavorite = async (icon: IconMetadata, event: React.MouseEvent) => {
    event.stopPropagation();
    // 这个功能现在由收藏夹管理系统处理，这里暂时禁用
    // console.log('收藏功能已迁移到新的收藏夹系统'); // 已清理调试代码
  };

  const handleIconRenderSuccess = (icon: IconMetadata) => {
    const iconId = `${icon.library?.name}-${icon.icon_key}`;
    setRenderStates(prev => ({
      ...prev,
      [iconId]: { loading: false, error: false, verified: true }
    }));
  };

  const handleIconRenderError = (icon: IconMetadata) => {
    const iconId = `${icon.library?.name}-${icon.icon_key}`;
    setRenderStates(prev => ({
      ...prev,
      [iconId]: { loading: false, error: true, verified: false }
    }));
  };

  const renderIconItem = (icon: IconMetadata) => {
    const iconId = `${icon.library?.name}-${icon.icon_key}`;
    const renderState = renderStates[iconId] || { loading: true, error: false, verified: false };
    const selected = isSelected(icon);

    if (viewMode === 'list') {
      return (
        <Card
          key={icon.id}
          size="small"
          className={`icon-item-list ${selected ? 'selected' : ''} ${renderState.error ? 'error' : ''} ${renderState.verified ? 'verified' : ''}`}
          onClick={() => onSelect(icon)}
          hoverable
        >
          <div className="icon-list-content">
            <div className="icon-preview-area">
              <UniversalIconRenderer
                library={icon.library?.name || ''}
                iconKey={icon.icon_key}
                size={iconSize}
                onLoadSuccess={() => handleIconRenderSuccess(icon)}
                onLoadError={() => handleIconRenderError(icon)}
              />
            </div>

            <div className="icon-info-area">
              <div className="icon-name">{icon.display_name || icon.icon_key}</div>
              <div className="icon-meta">
                <Tag size="small" color={getLibraryColor(icon.library?.name || '')}>
                  {icon.library?.display_name}
                </Tag>
                {icon.category && (
                  <Tag size="small">{icon.category.display_name}</Tag>
                )}
                {renderState.verified && <Tag size="small" color="green">已验证</Tag>}
                {renderState.error && <Tag size="small" color="red">渲染失败</Tag>}
              </div>
              {icon.description && (
                <div className="icon-description">{icon.description}</div>
              )}
            </div>

            <div className="icon-actions">
              <Button
                type="text"
                size="small"
                icon={icon.is_favorite ? <HeartFilled style={{ color: '#ff4d4f' }} /> : <HeartOutlined />}
                loading={favoriteLoading[icon.id]}
                onClick={(e) => handleToggleFavorite(icon, e)}
              />
              {onPreview && (
                <Button
                  type="text"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    onPreview(icon);
                  }}
                />
              )}
            </div>
          </div>
        </Card>
      );
    }

    // Grid view
    return (
      <Card
        key={icon.id}
        size="small"
        className={`icon-item-grid ${selected ? 'selected' : ''} ${renderState.error ? 'error' : ''} ${renderState.verified ? 'verified' : ''}`}
        onClick={() => onSelect(icon)}
        hoverable
        bodyStyle={{ padding: '12px' }}
      >
        <div className="icon-grid-content">
          <div className="icon-preview-area">
            <UniversalIconRenderer
              library={icon.library?.name || ''}
              iconKey={icon.icon_key}
              size={iconSize}
              onLoadSuccess={() => handleIconRenderSuccess(icon)}
              onLoadError={() => handleIconRenderError(icon)}
            />
          </div>

          <div className="icon-info">
            <Tooltip title={icon.display_name || icon.icon_key}>
              <div className="icon-name">{icon.display_name || icon.icon_key}</div>
            </Tooltip>
            <div className="icon-meta">
              <Tag size="small" color={getLibraryColor(icon.library?.name || '')}>
                {icon.library?.name}
              </Tag>
              {renderState.verified && <Tag size="small" color="green">✓</Tag>}
              {renderState.error && <Tag size="small" color="red">✗</Tag>}
            </div>
          </div>



          {/* 收藏按钮 */}
          <div className="favorite-toggle">
            <Button
              type="text"
              size="small"
              icon={icon.is_favorite ? <HeartFilled style={{ color: '#ff4d4f' }} /> : <HeartOutlined />}
              loading={favoriteLoading[icon.id]}
              onClick={(e) => handleToggleFavorite(icon, e)}
            />
          </div>

          {/* 预览按钮 */}
          {onPreview && (
            <div className="preview-button">
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  onPreview(icon);
                }}
              />
            </div>
          )}
        </div>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="icon-grid-loading">
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载图标中...</div>
      </div>
    );
  }

  if (icons.length === 0) {
    return (
      <div className="icon-grid-empty">
        <div>未找到匹配的图标</div>
        <div style={{ color: '#999', fontSize: '14px', marginTop: 8 }}>
          请尝试调整搜索条件或选择其他分类
        </div>
      </div>
    );
  }

  return (
    <div className={`icon-grid ${viewMode}`}>
      {icons.map(renderIconItem)}
    </div>
  );
};

export default IconGrid;
