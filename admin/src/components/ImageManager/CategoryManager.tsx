import React, { useState } from 'react';
import {
  Modal, Tree, Button, Form, Input, Select, ColorPicker, Space,
  Card, Row, Col, Typography, Tag, Popconfirm, message, Divider
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, FolderOutlined,
  SaveOutlined, CloseOutlined, DragOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstance from '../../api/axiosInstance';
import { useCategorySlug } from '../../hooks/useSlug';

const { Title, Text } = Typography;
const { TreeNode } = Tree;

interface CategoryData {
  id: number;
  name: string;
  slug: string;
  description?: string;
  color: string;
  icon?: string;
  image_count: number;
  is_system: boolean;
  is_active: boolean;
  parent_id?: number;
  children?: CategoryData[];
}

interface CategoryManagerProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CategoryManager: React.FC<CategoryManagerProps> = ({
  visible,
  onClose,
  onSuccess
}) => {
  const [selectedCategory, setSelectedCategory] = useState<CategoryData | null>(null);
  const [editMode, setEditMode] = useState<'create' | 'edit' | null>(null);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  // 使用slug Hook
  const slugHook = useCategorySlug({
    useBackend: true,
    autoGenerate: false
  });

  // 获取分类列表
  const { data: categories, isLoading } = useQuery({
    queryKey: ['image-categories-tree'],
    queryFn: async () => {
      const response = await axiosInstance.get('/image-categories/tree?include_inactive=true');
      return response.data;
    },
    enabled: visible
  });

  // 创建分类
  const createMutation = useMutation({
    mutationFn: async (data: any) => {
      // console.log('Creating category with data:', data); // 已清理调试代码
      // console.log('Data type check:', { // 已清理调试代码
        name: typeof data.name,
        slug: typeof data.slug,
        color: typeof data.color,
        is_active: typeof data.is_active
      });
      const response = await axiosInstance.post('/image-categories/', data);
      return response.data;
    },
    onSuccess: () => {
      message.success('分类创建成功');
      queryClient.invalidateQueries({ queryKey: ['image-categories-tree'] });
      queryClient.invalidateQueries({ queryKey: ['image-categories'] });
      setEditMode(null);
      form.resetFields();
      onSuccess();
    },
    onError: (error: any) => {
      console.error('Create category error:', error);
      console.error('Error response:', error.response);
      message.error(`创建失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  // 更新分类
  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: any }) => {
      const response = await axiosInstance.put(`/image-categories/${id}`, data);
      return response.data;
    },
    onSuccess: () => {
      message.success('分类更新成功');
      queryClient.invalidateQueries({ queryKey: ['image-categories-tree'] });
      queryClient.invalidateQueries({ queryKey: ['image-categories'] });
      setEditMode(null);
      form.resetFields();
      onSuccess();
    },
    onError: (error: any) => {
      message.error(`更新失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  // 删除分类
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      await axiosInstance.delete(`/image-categories/${id}`);
    },
    onSuccess: () => {
      message.success('分类删除成功');
      queryClient.invalidateQueries({ queryKey: ['image-categories-tree'] });
      queryClient.invalidateQueries({ queryKey: ['image-categories'] });
      setSelectedCategory(null);
      onSuccess();
    },
    onError: (error: any) => {
      message.error(`删除失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  const handleCreateCategory = () => {
    setEditMode('create');
    form.resetFields();
    form.setFieldsValue({
      color: '#1890ff',
      is_active: true,
      parent_id: selectedCategory?.id
    });
  };

  const handleEditCategory = () => {
    if (!selectedCategory) return;
    
    setEditMode('edit');
    form.setFieldsValue({
      name: selectedCategory.name,
      slug: selectedCategory.slug,
      description: selectedCategory.description,
      color: selectedCategory.color,
      icon: selectedCategory.icon,
      is_active: selectedCategory.is_active,
      parent_id: selectedCategory.parent_id
    });
  };

  const handleSave = async () => {
    // console.log('handleSave called, editMode:', editMode); // 已清理调试代码
    try {
      const values = await form.validateFields();
      // console.log('Form validation passed, values:', values); // 已清理调试代码

      // 处理颜色值格式
      if (values.color && typeof values.color === 'object') {
        values.color = values.color.toHexString();
      }

      // 生成slug（仅在创建时或名称改变时）
      if (editMode === 'create' || (editMode === 'edit' && values.name !== selectedCategory?.name)) {
        const existingSlugs = categories?.map(cat => cat.slug).filter(Boolean) || [];
        await slugHook.generateSlug(values.name, existingSlugs);
        values.slug = slugHook.slug;
      }

      // console.log('Submitting values:', values); // 添加调试日志 // 已清理调试代码

      if (editMode === 'create') {
        // console.log('Creating category...'); // 已清理调试代码
        createMutation.mutate(values);
      } else if (editMode === 'edit' && selectedCategory) {
        // console.log('Updating category...'); // 已清理调试代码
        updateMutation.mutate({ id: selectedCategory.id, data: values });
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleDelete = () => {
    if (!selectedCategory) return;
    
    if (selectedCategory.is_system) {
      message.error('系统分类不能删除');
      return;
    }

    if (selectedCategory.image_count > 0) {
      message.error('该分类下还有图片，请先移动或删除图片');
      return;
    }

    deleteMutation.mutate(selectedCategory.id);
  };



  const renderTreeNodes = (data: CategoryData[]): React.ReactNode => {
    return data.map((category) => (
      <TreeNode
        key={category.id}
        title={
          <Space>
            {category.icon && <span>{category.icon}</span>}
            <span style={{ color: category.color }}>{category.name}</span>
            <Tag color={category.color} size="small">
              {category.image_count}
            </Tag>
            {category.is_system && <Tag size="small">系统</Tag>}
            {!category.is_active && <Tag color="red" size="small">禁用</Tag>}
          </Space>
        }
        value={category.id}
      >
        {category.children && renderTreeNodes(category.children)}
      </TreeNode>
    ));
  };

  const flattenCategories = (categories: CategoryData[]): CategoryData[] => {
    const result: CategoryData[] = [];
    const flatten = (cats: CategoryData[]) => {
      cats.forEach(cat => {
        result.push(cat);
        if (cat.children) {
          flatten(cat.children);
        }
      });
    };
    flatten(categories);
    return result;
  };

  const allCategories = categories ? flattenCategories(categories) : [];

  return (
    <Modal
      title="分类管理"
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={null}
    >
      <Row gutter={24}>
        {/* 左侧：分类树 */}
        <Col span={12}>
          <Card
            title="分类列表"
            extra={
              <Button
                type="primary"
                size="small"
                icon={<PlusOutlined />}
                onClick={handleCreateCategory}
              >
                新建分类
              </Button>
            }
          >
            {categories && categories.length > 0 ? (
              <Tree
                showLine
                defaultExpandAll
                selectedKeys={selectedCategory ? [selectedCategory.id.toString()] : []}
                onSelect={(selectedKeys, info) => {
                  if (selectedKeys.length > 0) {
                    const categoryId = parseInt(selectedKeys[0] as string);
                    const category = allCategories.find(cat => cat.id === categoryId);
                    setSelectedCategory(category || null);
                  } else {
                    setSelectedCategory(null);
                  }
                }}
              >
                {renderTreeNodes(categories)}
              </Tree>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Text type="secondary">暂无分类</Text>
              </div>
            )}
          </Card>
        </Col>

        {/* 右侧：分类详情/编辑 */}
        <Col span={12}>
          {editMode ? (
            <Card
              title={editMode === 'create' ? '新建分类' : '编辑分类'}
              extra={
                <Space>
                  <Button
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={() => {
                      setEditMode(null);
                      form.resetFields();
                    }}
                  >
                    取消
                  </Button>
                  <Button
                    type="primary"
                    size="small"
                    icon={<SaveOutlined />}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleSave();
                    }}
                    loading={createMutation.isPending || updateMutation.isPending}
                  >
                    保存
                  </Button>
                </Space>
              }
            >
              <Form
                form={form}
                layout="vertical"
              >
                <Form.Item
                  label="分类名称"
                  name="name"
                  rules={[{ required: true, message: '请输入分类名称' }]}
                >
                  <Input
                    placeholder="输入分类名称"
                    onChange={(e) => {
                      const slug = generateSlug(e.target.value);
                      form.setFieldsValue({ slug });
                    }}
                  />
                </Form.Item>

                <Form.Item
                  label="分类标识"
                  name="slug"
                  rules={[{ required: true, message: '请输入分类标识' }]}
                >
                  <Input placeholder="分类标识（用于URL）" />
                </Form.Item>

                <Form.Item label="分类描述" name="description">
                  <Input.TextArea rows={3} placeholder="输入分类描述" />
                </Form.Item>

                <Form.Item label="父分类" name="parent_id">
                  <Select placeholder="选择父分类（可选）" allowClear>
                    {allCategories
                      .filter(cat => editMode === 'create' || cat.id !== selectedCategory?.id)
                      .map(category => (
                        <Select.Option key={category.id} value={category.id}>
                          <Space>
                            {category.icon && <span>{category.icon}</span>}
                            {category.name}
                          </Space>
                        </Select.Option>
                      ))}
                  </Select>
                </Form.Item>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="分类颜色"
                      name="color"
                      rules={[{ required: true, message: '请选择分类颜色' }]}
                    >
                      <ColorPicker showText />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="分类图标" name="icon">
                      <Input placeholder="图标（可选）" />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  label="状态"
                  name="is_active"
                  rules={[{ required: true, message: '请选择状态' }]}
                >
                  <Select>
                    <Select.Option value={true}>启用</Select.Option>
                    <Select.Option value={false}>禁用</Select.Option>
                  </Select>
                </Form.Item>
              </Form>
            </Card>
          ) : selectedCategory ? (
            <Card
              title="分类详情"
              extra={
                <Space>
                  <Button
                    size="small"
                    icon={<EditOutlined />}
                    onClick={handleEditCategory}
                    disabled={selectedCategory.is_system}
                  >
                    编辑
                  </Button>
                  <Popconfirm
                    title="确定要删除这个分类吗？"
                    description="删除后不可恢复，请确认该分类下没有图片。"
                    onConfirm={handleDelete}
                    okText="删除"
                    cancelText="取消"
                    disabled={selectedCategory.is_system || selectedCategory.image_count > 0}
                  >
                    <Button
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                      disabled={selectedCategory.is_system || selectedCategory.image_count > 0}
                    >
                      删除
                    </Button>
                  </Popconfirm>
                </Space>
              }
            >
              <div>
                <Title level={4} style={{ color: selectedCategory.color }}>
                  {selectedCategory.icon && <span>{selectedCategory.icon} </span>}
                  {selectedCategory.name}
                </Title>
                
                {selectedCategory.description && (
                  <Text type="secondary">{selectedCategory.description}</Text>
                )}

                <Divider />

                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Text strong>分类标识：</Text>
                    <br />
                    <Text code>{selectedCategory.slug}</Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>图片数量：</Text>
                    <br />
                    <Tag color={selectedCategory.color}>
                      {selectedCategory.image_count} 张
                    </Tag>
                  </Col>
                  <Col span={12}>
                    <Text strong>分类颜色：</Text>
                    <br />
                    <Tag color={selectedCategory.color}>
                      {selectedCategory.color}
                    </Tag>
                  </Col>
                  <Col span={12}>
                    <Text strong>状态：</Text>
                    <br />
                    <Tag color={selectedCategory.is_active ? 'green' : 'red'}>
                      {selectedCategory.is_active ? '启用' : '禁用'}
                    </Tag>
                  </Col>
                  {selectedCategory.is_system && (
                    <Col span={24}>
                      <Tag color="blue">系统分类</Tag>
                      <Text type="secondary">（系统分类不可删除和修改部分属性）</Text>
                    </Col>
                  )}
                </Row>
              </div>
            </Card>
          ) : (
            <Card>
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <FolderOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                <br />
                <Text type="secondary">请选择一个分类查看详情</Text>
              </div>
            </Card>
          )}
        </Col>
      </Row>
    </Modal>
  );
};

export default CategoryManager;
