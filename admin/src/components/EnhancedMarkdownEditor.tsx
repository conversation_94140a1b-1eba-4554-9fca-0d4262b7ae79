import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Button, Upload, message, Tabs, Modal, Input, Select,
  Tooltip, Space, Divider, ColorPicker, Dropdown, Menu,
  Row, Col, Card, Slider
} from 'antd';
import {
  BoldOutlined, ItalicOutlined, UnderlineOutlined,
  CodeOutlined, LinkOutlined, PictureOutlined,
  TableOutlined, OrderedListOutlined, UnorderedListOutlined,
  EyeOutlined, FullscreenOutlined, UploadOutlined,
  FontSizeOutlined, FontColorsOutlined, BgColorsOutlined,
  AlignLeftOutlined, AlignCenterOutlined, AlignRightOutlined,
  StrikethroughOutlined, HighlightOutlined, FormatPainterOutlined,
  QuestionCircleOutlined, RedoOutlined, UndoOutlined,
  ClearOutlined, SaveOutlined, MoreOutlined, SyncOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import type { UploadProps } from 'antd';
import './EnhancedMarkdownEditor.css';
import { BlogContent } from './blog/BlogContent';
import BlogImageSelector from './blog/BlogImageSelector';
// import { useMarkdownIconSelector } from '../hooks/useIconSelector';

const { TextArea } = Input;
const { Option } = Select;

// 改进的Markdown到HTML转换函数 - 与前端保持一致
function processMarkdownToHTML(content: string): string {
  try {
    let html = content

    // 首先保护已有的HTML标签，避免被markdown处理破坏
    const htmlTags: string[] = []
    let tagIndex = 0

    // 保护HTML标签
    html = html.replace(/<[^>]+>/g, (match) => {
      const placeholder = `__HTML_TAG_${tagIndex}__`
      htmlTags[tagIndex] = match
      tagIndex++
      return placeholder
    })

    // 处理代码块（在其他处理之前）- 使用特殊标记
    html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
      const language = lang || 'text'
      const cleanCode = code.trim()
      // 使用特殊标记，稍后在BlogContent组件中处理
      return `<div data-code-block="true" data-language="${language}">${cleanCode}</div>`
    })
    html = html.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>')

    // 处理标题
    html = html.replace(/^#### (.*$)/gim, '<h4>$1</h4>')
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>')
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>')
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>')

    // 处理粗体和斜体
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>')
    html = html.replace(/~~(.*?)~~/g, '<del>$1</del>')

    // 处理链接
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')

    // 处理图片
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" style="max-width: 100%; height: auto;" />')

    // 处理引用
    html = html.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>')

    // 处理水平线
    html = html.replace(/^---$/gm, '<hr>')

    // 处理表格
    const tableRegex = /(\|.*\|[\r\n]+\|.*\|[\r\n]+(?:\|.*\|[\r\n]*)*)/g
    html = html.replace(tableRegex, (match) => {
      const lines = match.trim().split('\n')
      if (lines.length < 2) return match

      const headers = lines[0].split('|').map(h => h.trim()).filter(h => h)
      const separator = lines[1]
      const rows = lines.slice(2).map(line =>
        line.split('|').map(cell => cell.trim()).filter(cell => cell)
      )

      let table = '<table style="border-collapse: collapse; width: 100%; margin: 1rem 0;">'
      table += '<thead><tr>'
      headers.forEach(header => {
        table += `<th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;">${header}</th>`
      })
      table += '</tr></thead><tbody>'

      rows.forEach(row => {
        table += '<tr>'
        row.forEach(cell => {
          table += `<td style="border: 1px solid #ddd; padding: 8px;">${cell}</td>`
        })
        table += '</tr>'
      })

      table += '</tbody></table>'
      return table
    })

    // 处理列表
    html = html.replace(/^(\d+)\. (.+)$/gm, '<li>$2</li>')
    html = html.replace(/^- (.+)$/gm, '<li>$1</li>')
    html = html.replace(/^(\* (.+))$/gm, '<li>$2</li>')

    // 将连续的li标签包装在ul或ol中
    html = html.replace(/(<li>.*?<\/li>[\s\n]*)+/gs, (match) => {
      return `<ul style="list-style-type: disc; padding-left: 1.5rem; margin: 1rem 0;">${match}</ul>`
    })

    // 处理段落
    const lines = html.split('\n')
    const processedLines: string[] = []
    let inParagraph = false

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()

      // 跳过空行
      if (!line) {
        if (inParagraph) {
          processedLines.push('</p>')
          inParagraph = false
        }
        continue
      }

      // 检查是否是HTML标签行
      if (line.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|table|hr|div)/)) {
        if (inParagraph) {
          processedLines.push('</p>')
          inParagraph = false
        }
        processedLines.push(line)
      } else {
        // 普通文本行
        if (!inParagraph) {
          processedLines.push('<p style="margin: 1rem 0; line-height: 1.6;">')
          inParagraph = true
        }
        processedLines.push(line + '<br>')
      }
    }

    if (inParagraph) {
      processedLines.push('</p>')
    }

    html = processedLines.join('\n')

    // 恢复HTML标签
    for (let i = 0; i < htmlTags.length; i++) {
      html = html.replace(`__HTML_TAG_${i}__`, htmlTags[i])
    }

    // 清理多余的br标签
    html = html.replace(/<br><\/p>/g, '</p>')
    html = html.replace(/<p><\/p>/g, '')

    return html
  } catch (error) {
    console.error('Markdown processing error:', error)
    // 如果处理失败，返回原始内容并添加基本的换行处理
    return content.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')
  }
}

interface EnhancedMarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  height?: number;
  placeholder?: string;
  autoSaveKey?: string; // 用于区分不同编辑器的自动保存
  onAutoSave?: (content: string) => void; // 自动保存回调
}

const EnhancedMarkdownEditor: React.FC<EnhancedMarkdownEditorProps> = ({
  value,
  onChange,
  height = 400,
  placeholder = "开始编写你的博客内容...",
  autoSaveKey = 'default',
  onAutoSave
}) => {
  const [activeTab, setActiveTab] = useState('edit');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [linkModalVisible, setLinkModalVisible] = useState(false);
  const [tableModalVisible, setTableModalVisible] = useState(false);
  const [imageSelectorVisible, setImageSelectorVisible] = useState(false);
  const [linkText, setLinkText] = useState('');
  const [linkUrl, setLinkUrl] = useState('');
  const [tableRows, setTableRows] = useState(3);
  const [tableCols, setTableCols] = useState(3);
  const [selectedFontSize, setSelectedFontSize] = useState(14);
  const [selectedTextColor, setSelectedTextColor] = useState('#000000');
  const [selectedBgColor, setSelectedBgColor] = useState('#ffff00');
  const [history, setHistory] = useState<string[]>([value]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [htmlModalVisible, setHtmlModalVisible] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [autoSaveStatus, setAutoSaveStatus] = useState<'saved' | 'saving' | 'unsaved'>('saved');

  // 新增状态：同步滚动和分屏模式
  const [isSplitView, setIsSplitView] = useState(false);
  const [syncScroll, setSyncScroll] = useState(true);
  const [isScrolling, setIsScrolling] = useState(false);
  const [showShortcuts, setShowShortcuts] = useState(false);

  // Refs
  const textAreaRef = useRef<any>(null);
  const previewRef = useRef<HTMLDivElement>(null);
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 图标选择器Hook (暂时注释)
  // const { openSelector: openIconSelector, IconSelectorComponent } = useMarkdownIconSelector(
  //   (markdown: string) => {
  //     insertText(markdown);
  //   }
  // );

  // 调试：监听value变化
  React.useEffect(() => {
    // 移除调试输出，使用统一日志系统
  }, [value]);

  // 自动保存功能
  const performAutoSave = useCallback(async (content: string) => {
    if (!content.trim()) return; // 空内容不保存

    setAutoSaveStatus('saving');

    try {
      // 保存到localStorage
      const autoSaveData = {
        content,
        timestamp: new Date().toISOString(),
        key: autoSaveKey
      };
      localStorage.setItem(`blog-autosave-${autoSaveKey}`, JSON.stringify(autoSaveData));

      // 如果有自定义保存回调，也执行它
      if (onAutoSave) {
        await onAutoSave(content);
      }

      setLastSaved(new Date());
      setAutoSaveStatus('saved');
      // console.log('自动保存成功:', content.length, '字符'); // 已清理调试代码
    } catch (error) {
      console.error('自动保存失败:', error);
      setAutoSaveStatus('unsaved');
    }
  }, [autoSaveKey, onAutoSave]);

  // 监听内容变化，设置自动保存定时器
  React.useEffect(() => {
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current);
    }

    if (value && value.trim()) {
      setAutoSaveStatus('unsaved');

      // 30秒后自动保存
      autoSaveTimerRef.current = setTimeout(() => {
        performAutoSave(value);
      }, 30000);
    }

    return () => {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
      }
    };
  }, [value, performAutoSave]);

  // 组件卸载时清理定时器
  React.useEffect(() => {
    return () => {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
      }
    };
  }, []);

  // 快捷键处理
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const { ctrlKey, metaKey, shiftKey, key } = e;
    const isCtrlOrCmd = ctrlKey || metaKey;

    if (isCtrlOrCmd) {
      switch (key) {
        case 'b':
          e.preventDefault();
          handleBold();
          break;
        case 'i':
          e.preventDefault();
          handleItalic();
          break;
        case 'u':
          e.preventDefault();
          handleUnderline();
          break;
        case 'k':
          e.preventDefault();
          setLinkModalVisible(true);
          break;
        case '`':
          e.preventDefault();
          handleInlineCode();
          break;
        case 's':
          e.preventDefault();
          performAutoSave(value);
          break;
        case 'z':
          e.preventDefault();
          if (shiftKey) {
            handleRedo();
          } else {
            handleUndo();
          }
          break;
        case '/':
          e.preventDefault();
          handleComment();
          break;
        default:
          break;
      }
    }

    // Tab键处理
    if (key === 'Tab') {
      e.preventDefault();
      const textarea = e.target as HTMLTextAreaElement;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newValue = value.substring(0, start) + '  ' + value.substring(end);
      onChange(newValue);

      // 设置光标位置
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + 2;
      }, 0);
    }
  }, [value, onChange, performAutoSave]);

  // 同步滚动处理
  const handleEditorScroll = useCallback((e: React.UIEvent<HTMLTextAreaElement>) => {
    if (!syncScroll || !isSplitView || isScrolling) return;

    const editor = e.target as HTMLTextAreaElement;
    const preview = previewRef.current;

    if (preview) {
      setIsScrolling(true);
      const scrollPercentage = editor.scrollTop / (editor.scrollHeight - editor.clientHeight);
      const previewScrollTop = scrollPercentage * (preview.scrollHeight - preview.clientHeight);
      preview.scrollTop = previewScrollTop;

      setTimeout(() => setIsScrolling(false), 100);
    }
  }, [syncScroll, isSplitView, isScrolling]);

  const handlePreviewScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    if (!syncScroll || !isSplitView || isScrolling) return;

    const preview = e.target as HTMLDivElement;
    const editor = textAreaRef.current?.resizableTextArea?.textArea;

    if (editor) {
      setIsScrolling(true);
      const scrollPercentage = preview.scrollTop / (preview.scrollHeight - preview.clientHeight);
      const editorScrollTop = scrollPercentage * (editor.scrollHeight - editor.clientHeight);
      editor.scrollTop = editorScrollTop;

      setTimeout(() => setIsScrolling(false), 100);
    }
  }, [syncScroll, isSplitView, isScrolling]);

  // 恢复自动保存的内容
  const restoreAutoSavedContent = useCallback(() => {
    try {
      const savedData = localStorage.getItem(`blog-autosave-${autoSaveKey}`);
      if (savedData) {
        const { content, timestamp } = JSON.parse(savedData);
        const savedTime = new Date(timestamp);
        const now = new Date();
        const diffHours = (now.getTime() - savedTime.getTime()) / (1000 * 60 * 60);

        // 只恢复24小时内的自动保存
        if (diffHours < 24 && content && content.trim() && content !== value) {
          Modal.confirm({
            title: '发现自动保存的内容',
            content: (
              <div>
                <p>发现 {savedTime.toLocaleString()} 自动保存的内容，是否恢复？</p>
                <p className="text-gray-500">内容长度: {content.length} 字符</p>
              </div>
            ),
            onOk: () => {
              onChange(content);
              message.success('已恢复自动保存的内容');
            },
            onCancel: () => {
              // 用户选择不恢复，删除自动保存
              localStorage.removeItem(`blog-autosave-${autoSaveKey}`);
            }
          });
        }
      }
    } catch (error) {
      console.error('恢复自动保存内容失败:', error);
    }
  }, [autoSaveKey, value, onChange]);

  // 组件挂载时检查是否有自动保存的内容
  React.useEffect(() => {
    // 延迟检查，确保初始value已经设置
    const timer = setTimeout(() => {
      restoreAutoSavedContent();
    }, 1000);

    return () => clearTimeout(timer);
  }, []); // 只在组件挂载时执行一次

  // 手动保存功能
  const handleManualSave = useCallback(() => {
    if (value && value.trim()) {
      performAutoSave(value);
      message.success('手动保存成功');
    }
  }, [value, performAutoSave]);

  // 清除自动保存
  const clearAutoSave = useCallback(() => {
    localStorage.removeItem(`blog-autosave-${autoSaveKey}`);
    setLastSaved(null);
    setAutoSaveStatus('saved');
    message.success('已清除自动保存的内容');
  }, [autoSaveKey]);

  // 添加到历史记录
  const addToHistory = (newValue: string) => {
    if (newValue !== value) {
      const newHistory = history.slice(0, historyIndex + 1);
      newHistory.push(newValue);
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
    }
  };

  // 撤销
  const handleUndo = () => {
    if (historyIndex > 0) {
      const prevValue = history[historyIndex - 1];
      setHistoryIndex(historyIndex - 1);
      onChange(prevValue);
    }
  };

  // 重做
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      const nextValue = history[historyIndex + 1];
      setHistoryIndex(historyIndex + 1);
      onChange(nextValue);
    }
  };

  // 插入文本到光标位置
  const insertText = (text: string, selectText = false) => {
    const textarea = textAreaRef.current?.resizableTextArea?.textArea;
    if (!textarea) {
      // 如果无法获取textarea，直接在末尾添加
      const newValue = value + text;
      onChange(newValue);
      addToHistory(newValue);
      return;
    }

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const newValue = value.substring(0, start) + text + value.substring(end);

    onChange(newValue);
    addToHistory(newValue);

    // 设置光标位置
    setTimeout(() => {
      if (selectText) {
        textarea.setSelectionRange(start + 1, start + text.length - 1);
      } else {
        textarea.setSelectionRange(start + text.length, start + text.length);
      }
      textarea.focus();
    }, 0);
  };

  // 格式化文本的通用函数（支持选中文本的包装）
  const formatText = (before: string, after: string = '', placeholder: string = '') => {
    const textarea = textAreaRef.current?.resizableTextArea?.textArea;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    const textToInsert = selectedText || placeholder;
    const newText = before + textToInsert + after;

    const newValue = value.substring(0, start) + newText + value.substring(end);
    onChange(newValue);
    addToHistory(newValue);

    // 设置光标位置
    setTimeout(() => {
      const newStart = start + before.length;
      const newEnd = newStart + textToInsert.length;
      textarea.focus();
      textarea.setSelectionRange(newStart, newEnd);
    }, 0);
  };

  // 新增格式化函数
  const handleInlineCode = () => formatText('`', '`', '代码');
  const handleComment = () => formatText('<!-- ', ' -->', '注释');
  const handleUnderline = () => formatText('<u>', '</u>', '下划线文本');

  // 格式化功能 - 更新为使用formatText
  const handleBold = () => formatText('**', '**', '粗体文本');
  const handleItalic = () => formatText('*', '*', '斜体文本');
  const handleStrikethrough = () => formatText('~~', '~~', '删除线文本');
  const handleCode = () => formatText('`', '`', '代码');
  // const handleCodeBlock = () => insertText('\n```javascript\n// 代码块\nconsole.log("Hello World");\n```\n'); // 已清理调试代码
  const handleOrderedList = () => insertText('\n1. 列表项\n2. 列表项\n3. 列表项\n');
  const handleUnorderedList = () => insertText('\n- 列表项\n- 列表项\n- 列表项\n');
  const handleQuote = () => insertText('\n> 引用文本\n> 可以多行\n');
  const handleDivider = () => insertText('\n\n---\n\n');

  // 标题
  const handleHeading = (level: number) => {
    const prefix = '#'.repeat(level);
    insertText(`\n${prefix} 标题${level}\n`);
  };

  // 字体大小
  const handleFontSize = (size: number) => {
    setSelectedFontSize(size);
    insertText(`<span style="font-size: ${size}px">调整字体大小</span>`, true);
  };

  // 文字颜色
  const handleTextColor = (color: string) => {
    setSelectedTextColor(color);
    insertText(`<span style="color: ${color}">彩色文字</span>`, true);
  };

  // 背景颜色/高亮
  const handleBgColor = (color: string) => {
    setSelectedBgColor(color);
    insertText(`<span style="background-color: ${color}; padding: 2px 4px; border-radius: 2px;">高亮文字</span>`, true);
  };

  // 对齐方式
  const handleAlign = (align: 'left' | 'center' | 'right') => {
    const alignText = align === 'left' ? '左对齐' : align === 'center' ? '居中对齐' : '右对齐';
    insertText(`<div style="text-align: ${align}">${alignText}文本</div>`, true);
  };

  // 清空内容
  const handleClear = () => {
    Modal.confirm({
      title: '确认清空',
      content: '确定要清空所有内容吗？此操作不可撤销。',
      onOk: () => {
        onChange('');
        addToHistory('');
      }
    });
  };

  // 显示HTML渲染结果
  const handleShowHTML = () => {
    setHtmlModalVisible(true);
  };

  // 复制HTML到剪贴板
  const handleCopyHTML = async () => {
    const html = processMarkdownToHTML(value || '');
    try {
      await navigator.clipboard.writeText(html);
      message.success('HTML代码已复制到剪贴板');
    } catch (err) {
      message.error('复制失败，请手动复制');
    }
  };

  // 插入链接
  const handleInsertLink = () => {
    if (linkText && linkUrl) {
      insertText(`[${linkText}](${linkUrl})`);
      setLinkText('');
      setLinkUrl('');
      setLinkModalVisible(false);
    }
  };

  // 插入表格
  const handleInsertTable = () => {
    let tableText = '\n';
    // 表头
    tableText += '| ' + Array(tableCols).fill('列标题').map((_, i) => `列${i + 1}`).join(' | ') + ' |\n';
    // 分隔线
    tableText += '| ' + Array(tableCols).fill('---').join(' | ') + ' |\n';
    // 数据行
    for (let i = 0; i < tableRows; i++) {
      tableText += '| ' + Array(tableCols).fill('数据').join(' | ') + ' |\n';
    }
    tableText += '\n';
    
    insertText(tableText);
    setTableModalVisible(false);
  };

  // 处理图片插入
  const handleImageInsert = (imageData: any) => {
    if (imageData.markdown) {
      insertText(imageData.markdown);
    }
  };

  // 上传配置（保留作为备用）
  const uploadProps: UploadProps = {
    name: 'file',
    action: '/api/upload',
    onChange(info) {
      if (info.file.status === 'done') {
        const imageUrl = info.file.response?.url;
        if (imageUrl) {
          insertText(`![图片描述](${imageUrl})`);
          message.success('图片上传成功');
        }
      } else if (info.file.status === 'error') {
        message.error('图片上传失败');
      }
    },
    showUploadList: false,
    accept: 'image/*'
  };

  return (
    <div
      style={{
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        backgroundColor: '#fff',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
        overflow: 'hidden',
        ...(isFullscreen && {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 1000,
          backgroundColor: 'white',
          borderRadius: 0,
          border: 'none'
        })
      }}
    >
      {/* 增强工具栏 */}
      <div
        className="enhanced-editor-toolbar"
        style={{
          padding: '12px 16px',
          borderBottom: '1px solid #d9d9d9',
          backgroundColor: '#fafafa'
        }}
      >
        {/* 第一行：历史操作和基础格式 */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          marginBottom: '12px',
          flexWrap: 'wrap'
        }}>
          {/* 历史操作 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Tooltip title="撤销">
              <Button
                size="small"
                icon={<UndoOutlined />}
                onClick={handleUndo}
                disabled={historyIndex <= 0}
              />
            </Tooltip>
            <Tooltip title="重做">
              <Button
                size="small"
                icon={<RedoOutlined />}
                onClick={handleRedo}
                disabled={historyIndex >= history.length - 1}
              />
            </Tooltip>
            <Tooltip title="清空内容">
              <Button
                size="small"
                icon={<ClearOutlined />}
                onClick={handleClear}
                danger
              />
            </Tooltip>
            <Tooltip title="渲染为HTML">
              <Button
                size="small"
                icon={<CodeOutlined />}
                onClick={handleShowHTML}
                type="primary"
                ghost
              />
            </Tooltip>
          </div>

          <Divider type="vertical" style={{ height: '20px' }} />

          {/* 自动保存状态 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Tooltip title="手动保存">
              <Button
                size="small"
                icon={<SaveOutlined />}
                onClick={handleManualSave}
                loading={autoSaveStatus === 'saving'}
                type={autoSaveStatus === 'unsaved' ? 'primary' : 'default'}
              />
            </Tooltip>

            <div style={{ display: 'flex', alignItems: 'center', gap: '4px', fontSize: '12px' }}>
              {autoSaveStatus === 'saved' && (
                <span style={{ color: '#52c41a' }}>
                  ✓ 已保存
                  {lastSaved && (
                    <span style={{ color: '#8c8c8c', marginLeft: '4px' }}>
                      {lastSaved.toLocaleTimeString()}
                    </span>
                  )}
                </span>
              )}
              {autoSaveStatus === 'saving' && (
                <span style={{ color: '#1890ff' }}>
                  ⏳ 保存中...
                </span>
              )}
              {autoSaveStatus === 'unsaved' && (
                <span style={{ color: '#faad14' }}>
                  ⚠ 未保存
                </span>
              )}
            </div>

            <Dropdown
              overlay={
                <Menu>
                  <Menu.Item key="clear" onClick={clearAutoSave}>
                    清除自动保存
                  </Menu.Item>
                  <Menu.Item key="restore" onClick={restoreAutoSavedContent}>
                    恢复自动保存
                  </Menu.Item>
                </Menu>
              }
              trigger={['click']}
            >
              <Button size="small" icon={<MoreOutlined />} />
            </Dropdown>
          </div>

          <Divider type="vertical" style={{ height: '20px' }} />

          {/* 标题和字体 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Tooltip title="标题级别">
              <Select
                placeholder="标题"
                style={{ width: 80 }}
                size="small"
                onChange={handleHeading}
                value={undefined}
              >
                <Option value={1}>H1</Option>
                <Option value={2}>H2</Option>
                <Option value={3}>H3</Option>
                <Option value={4}>H4</Option>
                <Option value={5}>H5</Option>
                <Option value={6}>H6</Option>
              </Select>
            </Tooltip>

            <Dropdown
              overlay={
                <Menu>
                  <Menu.Item key="12" onClick={() => handleFontSize(12)}>
                    <span style={{ fontSize: '12px' }}>12px 小字体</span>
                  </Menu.Item>
                  <Menu.Item key="14" onClick={() => handleFontSize(14)}>
                    <span style={{ fontSize: '14px' }}>14px 正常</span>
                  </Menu.Item>
                  <Menu.Item key="16" onClick={() => handleFontSize(16)}>
                    <span style={{ fontSize: '16px' }}>16px 中等</span>
                  </Menu.Item>
                  <Menu.Item key="18" onClick={() => handleFontSize(18)}>
                    <span style={{ fontSize: '18px' }}>18px 大字体</span>
                  </Menu.Item>
                  <Menu.Item key="20" onClick={() => handleFontSize(20)}>
                    <span style={{ fontSize: '20px' }}>20px 特大</span>
                  </Menu.Item>
                  <Menu.Item key="24" onClick={() => handleFontSize(24)}>
                    <span style={{ fontSize: '24px' }}>24px 超大</span>
                  </Menu.Item>
                </Menu>
              }
              trigger={['click']}
            >
              <Tooltip title="字体大小">
                <Button size="small" icon={<FontSizeOutlined />} />
              </Tooltip>
            </Dropdown>
          </div>
        </div>

        {/* 第二行：颜色和格式化 */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          flexWrap: 'wrap'
        }}>
          {/* 颜色工具 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Tooltip title="文字颜色">
              <ColorPicker
                size="small"
                value={selectedTextColor}
                onChange={(color) => handleTextColor(color.toHexString())}
                trigger="click"
                showText
              >
                <Button size="small" icon={<FontColorsOutlined />} />
              </ColorPicker>
            </Tooltip>

            <Tooltip title="背景高亮">
              <ColorPicker
                size="small"
                value={selectedBgColor}
                onChange={(color) => handleBgColor(color.toHexString())}
                trigger="click"
                showText
              >
                <Button size="small" icon={<BgColorsOutlined />} />
              </ColorPicker>
            </Tooltip>
          </div>

          <Divider type="vertical" style={{ height: '20px' }} />

          {/* 格式化按钮 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Tooltip title="粗体 (Ctrl+B)">
              <Button size="small" icon={<BoldOutlined />} onClick={handleBold} />
            </Tooltip>
            <Tooltip title="斜体 (Ctrl+I)">
              <Button size="small" icon={<ItalicOutlined />} onClick={handleItalic} />
            </Tooltip>
            <Tooltip title="下划线 (Ctrl+U)">
              <Button size="small" icon={<UnderlineOutlined />} onClick={handleUnderline} />
            </Tooltip>
            <Tooltip title="删除线">
              <Button size="small" icon={<StrikethroughOutlined />} onClick={handleStrikethrough} />
            </Tooltip>
            <Tooltip title="行内代码">
              <Button size="small" icon={<CodeOutlined />} onClick={handleCode} />
            </Tooltip>
          </div>

          <Divider type="vertical" style={{ height: '20px' }} />

          {/* 对齐方式 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Tooltip title="左对齐">
              <Button size="small" icon={<AlignLeftOutlined />} onClick={() => handleAlign('left')} />
            </Tooltip>
            <Tooltip title="居中对齐">
              <Button size="small" icon={<AlignCenterOutlined />} onClick={() => handleAlign('center')} />
            </Tooltip>
            <Tooltip title="右对齐">
              <Button size="small" icon={<AlignRightOutlined />} onClick={() => handleAlign('right')} />
            </Tooltip>
          </div>

          <Divider type="vertical" style={{ height: '20px' }} />

          {/* 列表和其他 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Tooltip title="有序列表">
              <Button size="small" icon={<OrderedListOutlined />} onClick={handleOrderedList} />
            </Tooltip>
            <Tooltip title="无序列表">
              <Button size="small" icon={<UnorderedListOutlined />} onClick={handleUnorderedList} />
            </Tooltip>
            <Tooltip title="引用">
              <Button size="small" icon={<QuestionCircleOutlined />} onClick={handleQuote} />
            </Tooltip>
            <Tooltip title="分割线">
              <Button size="small" onClick={handleDivider}>
                ---
              </Button>
            </Tooltip>
          </div>

          <Divider type="vertical" style={{ height: '20px' }} />

          {/* 插入功能 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Tooltip title="插入链接">
              <Button
                size="small"
                icon={<LinkOutlined />}
                onClick={() => setLinkModalVisible(true)}
              />
            </Tooltip>

            <Tooltip title="插入图片">
              <Button
                size="small"
                icon={<PictureOutlined />}
                onClick={() => setImageSelectorVisible(true)}
              />
            </Tooltip>

            {/* 暂时注释图标按钮 */}
            {/* <Tooltip title="插入图标">
              <Button
                size="small"
                icon={<AppstoreOutlined />}
                onClick={openIconSelector}
              />
            </Tooltip> */}

            <Tooltip title="插入表格">
              <Button
                size="small"
                icon={<TableOutlined />}
                onClick={() => setTableModalVisible(true)}
              />
            </Tooltip>

            <Tooltip title="代码块">
              <Button size="small" onClick={handleCodeBlock}>
                {'{}'}
              </Button>
            </Tooltip>
          </div>

          <Divider type="vertical" style={{ height: '20px' }} />

          {/* 视图控制 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Tooltip title={isSplitView ? "切换到标签模式" : "分屏模式"}>
              <Button
                size="small"
                type={isSplitView ? 'primary' : 'default'}
                onClick={() => setIsSplitView(!isSplitView)}
              >
                分屏
              </Button>
            </Tooltip>

            {isSplitView && (
              <Tooltip title={syncScroll ? "关闭同步滚动" : "开启同步滚动"}>
                <Button
                  size="small"
                  type={syncScroll ? 'primary' : 'default'}
                  icon={<SyncOutlined />}
                  onClick={() => setSyncScroll(!syncScroll)}
                />
              </Tooltip>
            )}

            <Tooltip title="快捷键提示">
              <Button
                size="small"
                icon={<QuestionCircleOutlined />}
                onClick={() => setShowShortcuts(!showShortcuts)}
                type={showShortcuts ? 'primary' : 'default'}
              />
            </Tooltip>

            <Tooltip title={isFullscreen ? "退出全屏" : "全屏编辑"}>
              <Button
                size="small"
                icon={<FullscreenOutlined />}
                onClick={() => setIsFullscreen(!isFullscreen)}
              />
            </Tooltip>
          </div>
        </div>
      </div>

      {/* 编辑器内容区域 */}
      <div style={{ position: 'relative' }}>
        {isSplitView ? (
          // 分屏模式
          <div style={{ display: 'flex', height: isFullscreen ? 'calc(100vh - 180px)' : height - 100 }}>
            {/* 编辑区域 */}
            <div style={{ flex: 1, borderRight: '1px solid #d9d9d9' }}>
              <div style={{
                padding: '8px 12px',
                backgroundColor: '#fafafa',
                borderBottom: '1px solid #d9d9d9',
                fontSize: '12px',
                fontWeight: 'bold'
              }}>
                ✏️ 编辑
              </div>
              <div style={{ position: 'relative' }}>
                <TextArea
                  ref={textAreaRef}
                  className="enhanced-editor-textarea"
                  value={value}
                  onChange={(e) => {
                    const newValue = e.target.value;
                    onChange(newValue);
                    addToHistory(newValue);
                  }}
                  onKeyDown={handleKeyDown}
                  onScroll={handleEditorScroll}
                  placeholder={placeholder}
                  style={{
                    border: 'none',
                    resize: 'none',
                    height: isFullscreen ? 'calc(100vh - 220px)' : height - 180,
                    fontSize: '14px',
                    lineHeight: '1.6',
                    padding: '16px',
                    backgroundColor: '#fff',
                    borderRadius: 0
                  }}
                  autoSize={false}
                />
                {/* 字符统计 */}
                <div className="enhanced-editor-char-count">
                  {value.length} 字符
                </div>
              </div>
            </div>

            {/* 预览区域 */}
            <div style={{ flex: 1 }}>
              <div style={{
                padding: '8px 12px',
                backgroundColor: '#fafafa',
                borderBottom: '1px solid #d9d9d9',
                fontSize: '12px',
                fontWeight: 'bold'
              }}>
                👁️ 预览
              </div>
              <div
                ref={previewRef}
                className="enhanced-editor-preview"
                onScroll={handlePreviewScroll}
                style={{
                  padding: '16px',
                  height: isFullscreen ? 'calc(100vh - 220px)' : height - 180,
                  overflow: 'auto',
                  backgroundColor: '#fff',
                  fontSize: '14px',
                  lineHeight: '1.6',
                  color: '#333'
                }}
              >
                <BlogContent
                  content={processMarkdownToHTML(value || '')}
                  theme="light"
                />
              </div>
            </div>
          </div>
        ) : (
          // 标签模式
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            centered
            size="small"
            className="enhanced-editor-tabs"
            style={{
              margin: 0
            }}
            items={[
              {
                key: 'edit',
                label: (
                  <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                    ✏️ 编辑
                  </span>
                ),
                children: (
                  <div style={{ position: 'relative' }}>
                    <TextArea
                      ref={textAreaRef}
                      className="enhanced-editor-textarea"
                      value={value}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        onChange(newValue);
                        addToHistory(newValue);
                      }}
                      onKeyDown={handleKeyDown}
                      placeholder={placeholder}
                      style={{
                        border: 'none',
                        resize: 'none',
                        height: isFullscreen ? 'calc(100vh - 220px)' : height - 140,
                        fontSize: '14px',
                        lineHeight: '1.6',
                        padding: '16px',
                        backgroundColor: '#fff',
                        borderRadius: 0
                      }}
                      autoSize={false}
                    />
                    {/* 字符统计 */}
                    <div className="enhanced-editor-char-count">
                      {value.length} 字符
                    </div>
                  </div>
                )
              },
              {
                key: 'preview',
                label: (
                  <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                    👁️ 预览
                  </span>
                ),
                children: (
                  <div
                    className="enhanced-editor-preview"
                    style={{
                      padding: '16px',
                      height: isFullscreen ? 'calc(100vh - 220px)' : height - 140,
                      overflow: 'auto',
                      backgroundColor: '#fff',
                      fontSize: '14px',
                      lineHeight: '1.6',
                      color: '#333'
                    }}
                  >
                    <BlogContent
                      content={processMarkdownToHTML(value || '')}
                      theme="light"
                    />
                  </div>
                )
              }
            ]}
          />
        )}
      </div>

      {/* 链接插入模态框 */}
      <Modal
        title="插入链接"
        open={linkModalVisible}
        onOk={handleInsertLink}
        onCancel={() => setLinkModalVisible(false)}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Input
            placeholder="链接文字"
            value={linkText}
            onChange={(e) => setLinkText(e.target.value)}
          />
          <Input
            placeholder="链接地址"
            value={linkUrl}
            onChange={(e) => setLinkUrl(e.target.value)}
          />
        </Space>
      </Modal>

      {/* 表格插入模态框 */}
      <Modal
        title="插入表格"
        open={tableModalVisible}
        onOk={handleInsertTable}
        onCancel={() => setTableModalVisible(false)}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <span>行数：</span>
            <Slider
              min={1}
              max={10}
              value={tableRows}
              onChange={setTableRows}
              style={{ width: 200, marginLeft: 16 }}
            />
            <span style={{ marginLeft: 16 }}>{tableRows}</span>
          </div>
          <div>
            <span>列数：</span>
            <Slider
              min={1}
              max={8}
              value={tableCols}
              onChange={setTableCols}
              style={{ width: 200, marginLeft: 16 }}
            />
            <span style={{ marginLeft: 16 }}>{tableCols}</span>
          </div>
        </Space>
      </Modal>

      {/* HTML渲染模态框 */}
      <Modal
        title="HTML渲染结果"
        open={htmlModalVisible}
        onCancel={() => setHtmlModalVisible(false)}
        width={800}
        footer={[
          <Button key="copy" onClick={handleCopyHTML}>
            复制HTML代码
          </Button>,
          <Button key="close" onClick={() => setHtmlModalVisible(false)}>
            关闭
          </Button>
        ]}
      >
        <div style={{ marginBottom: 16 }}>
          <h4>预览效果：</h4>
          <div
            style={{
              border: '1px solid #d9d9d9',
              padding: '16px',
              borderRadius: '6px',
              backgroundColor: '#fafafa',
              maxHeight: '300px',
              overflow: 'auto'
            }}
          >
            <BlogContent
              content={processMarkdownToHTML(value || '')}
              theme="light"
            />
          </div>
        </div>
        <div>
          <h4>HTML代码：</h4>
          <Input.TextArea
            value={processMarkdownToHTML(value || '')}
            rows={10}
            readOnly
            style={{ fontFamily: 'monospace', fontSize: '12px' }}
          />
        </div>
      </Modal>

      {/* 快捷键提示面板 */}
      {showShortcuts && (
        <div className="enhanced-editor-shortcuts visible">
          <div style={{ marginBottom: '8px', fontWeight: 'bold', borderBottom: '1px solid rgba(255,255,255,0.3)', paddingBottom: '4px' }}>
            快捷键
          </div>
          <div className="enhanced-editor-shortcuts-item">
            <span>粗体</span>
            <span className="enhanced-editor-shortcuts-key">Ctrl+B</span>
          </div>
          <div className="enhanced-editor-shortcuts-item">
            <span>斜体</span>
            <span className="enhanced-editor-shortcuts-key">Ctrl+I</span>
          </div>
          <div className="enhanced-editor-shortcuts-item">
            <span>下划线</span>
            <span className="enhanced-editor-shortcuts-key">Ctrl+U</span>
          </div>
          <div className="enhanced-editor-shortcuts-item">
            <span>插入链接</span>
            <span className="enhanced-editor-shortcuts-key">Ctrl+K</span>
          </div>
          <div className="enhanced-editor-shortcuts-item">
            <span>内联代码</span>
            <span className="enhanced-editor-shortcuts-key">Ctrl+`</span>
          </div>
          <div className="enhanced-editor-shortcuts-item">
            <span>保存</span>
            <span className="enhanced-editor-shortcuts-key">Ctrl+S</span>
          </div>
          <div className="enhanced-editor-shortcuts-item">
            <span>撤销</span>
            <span className="enhanced-editor-shortcuts-key">Ctrl+Z</span>
          </div>
          <div className="enhanced-editor-shortcuts-item">
            <span>重做</span>
            <span className="enhanced-editor-shortcuts-key">Ctrl+Shift+Z</span>
          </div>
          <div className="enhanced-editor-shortcuts-item">
            <span>注释</span>
            <span className="enhanced-editor-shortcuts-key">Ctrl+/</span>
          </div>
          <div className="enhanced-editor-shortcuts-item">
            <span>缩进</span>
            <span className="enhanced-editor-shortcuts-key">Tab</span>
          </div>
        </div>
      )}

      {/* 图片选择器 */}
      <BlogImageSelector
        visible={imageSelectorVisible}
        onClose={() => setImageSelectorVisible(false)}
        onInsert={handleImageInsert}
      />

      {/* 图标选择器 (暂时注释) */}
      {/* <IconSelectorComponent /> */}
    </div>
  );
};

export default EnhancedMarkdownEditor;
