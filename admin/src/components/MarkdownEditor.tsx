import React, { useState, useRef } from 'react';
import {
  Button, Upload, message, Tabs, Modal, Input, Select,
  Tooltip, Space, Divider, ColorPicker, Dropdown, Menu,
  Slider, Row, Col, Card
} from 'antd';
import {
  BoldOutlined, ItalicOutlined, UnderlineOutlined,
  CodeOutlined, LinkOutlined, PictureOutlined,
  TableOutlined, OrderedListOutlined, UnorderedListOutlined,
  EyeOutlined, FullscreenOutlined, UploadOutlined,
  FontSizeOutlined, FontColorsOutlined, BgColorsOutlined,
  AlignLeftOutlined, AlignCenterOutlined, AlignRightOutlined,
  StrikethroughOutlined, HighlightOutlined, FormatPainterOutlined,
  QuestionCircleOutlined, RedoOutlined, UndoOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import type { UploadProps, TabsProps } from 'antd';
// import { useMarkdownIconSelector } from '../hooks/useIconSelector';

const { TextArea } = Input;
const { Option } = Select;

interface MarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  height?: number;
  placeholder?: string;
}

const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  value,
  onChange,
  height = 400,
  placeholder = "开始编写你的博客内容..."
}) => {
  const [activeTab, setActiveTab] = useState('edit');
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 调试：监听value变化
  React.useEffect(() => {
    // console.log('MarkdownEditor 接收到 value 变化:', value?.length || 0, '字符'); // 已清理调试代码
    // console.log('MarkdownEditor value 内容预览:', value?.substring(0, 100) || '空'); // 已清理调试代码
  }, [value]);
  const [linkModalVisible, setLinkModalVisible] = useState(false);
  const [tableModalVisible, setTableModalVisible] = useState(false);
  const [linkText, setLinkText] = useState('');
  const [linkUrl, setLinkUrl] = useState('');
  const [tableRows, setTableRows] = useState(3);
  const [tableCols, setTableCols] = useState(3);
  const [selectedFontSize, setSelectedFontSize] = useState(14);
  const [selectedTextColor, setSelectedTextColor] = useState('#000000');
  const [selectedBgColor, setSelectedBgColor] = useState('#ffffff');
  const [history, setHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const textAreaRef = useRef<any>(null);

  // 图标选择器Hook (暂时注释)
  // const { openSelector: openIconSelector, IconSelectorComponent } = useMarkdownIconSelector(
  //   (markdown: string) => {
  //     insertText(markdown);
  //   }
  // );

  // 添加到历史记录
  const addToHistory = (newValue: string) => {
    if (newValue !== value) {
      const newHistory = history.slice(0, historyIndex + 1);
      newHistory.push(newValue);
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
    }
  };

  // 撤销
  const handleUndo = () => {
    if (historyIndex > 0) {
      const prevValue = history[historyIndex - 1];
      setHistoryIndex(historyIndex - 1);
      onChange(prevValue);
    }
  };

  // 重做
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      const nextValue = history[historyIndex + 1];
      setHistoryIndex(historyIndex + 1);
      onChange(nextValue);
    }
  };

  // 插入文本到光标位置
  const insertText = (text: string, selectText = false) => {
    const textarea = textAreaRef.current?.resizableTextArea?.textArea;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const newValue = value.substring(0, start) + text + value.substring(end);

    onChange(newValue);

    // 设置光标位置
    setTimeout(() => {
      if (selectText) {
        textarea.setSelectionRange(start + 1, start + text.length - 1);
      } else {
        textarea.setSelectionRange(start + text.length, start + text.length);
      }
      textarea.focus();
    }, 0);
  };

  // 工具栏按钮处理函数
  const handleBold = () => insertText('**粗体文本**', true);
  const handleItalic = () => insertText('*斜体文本*', true);
  const handleUnderline = () => insertText('<u>下划线文本</u>', true);
  const handleStrikethrough = () => insertText('~~删除线文本~~', true);
  const handleCode = () => insertText('`代码`', true);
  // const handleCodeBlock = () => insertText('\n```javascript\n// 代码块\nconsole.log("Hello World");\n```\n'); // 已清理调试代码
  const handleOrderedList = () => insertText('\n1. 列表项\n2. 列表项\n3. 列表项\n');
  const handleUnorderedList = () => insertText('\n- 列表项\n- 列表项\n- 列表项\n');

  // 字体大小
  const handleFontSize = (size: number) => {
    setSelectedFontSize(size);
    const sizeText = size <= 12 ? '小' : size <= 16 ? '中' : '大';
    insertText(`<span style="font-size: ${size}px">${sizeText}号字体</span>`, true);
  };

  // 文字颜色
  const handleTextColor = (color: string) => {
    setSelectedTextColor(color);
    insertText(`<span style="color: ${color}">彩色文字</span>`, true);
  };

  // 背景颜色
  const handleBgColor = (color: string) => {
    setSelectedBgColor(color);
    insertText(`<span style="background-color: ${color}">高亮文字</span>`, true);
  };

  // 对齐方式
  const handleAlign = (align: 'left' | 'center' | 'right') => {
    const alignText = align === 'left' ? '左对齐' : align === 'center' ? '居中对齐' : '右对齐';
    insertText(`<div style="text-align: ${align}">${alignText}文本</div>`, true);
  };

  // 插入分割线
  const handleDivider = () => insertText('\n\n---\n\n');

  // 插入引用
  const handleQuote = () => insertText('\n> 引用文本\n> 可以多行\n');

  // 插入标题
  const handleHeading = (level: number) => {
    const prefix = '#'.repeat(level);
    insertText(`\n${prefix} 标题${level}\n`);
  };

  // 插入链接
  const handleInsertLink = () => {
    if (linkText && linkUrl) {
      insertText(`[${linkText}](${linkUrl})`);
      setLinkModalVisible(false);
      setLinkText('');
      setLinkUrl('');
    }
  };

  // 插入表格
  const handleInsertTable = () => {
    let table = '\n';
    // 表头
    table += '| ' + Array(tableCols).fill('表头').join(' | ') + ' |\n';
    // 分隔线
    table += '| ' + Array(tableCols).fill('---').join(' | ') + ' |\n';
    // 数据行
    for (let i = 0; i < tableRows; i++) {
      table += '| ' + Array(tableCols).fill('数据').join(' | ') + ' |\n';
    }
    table += '\n';

    insertText(table);
    setTableModalVisible(false);
  };

  // 图片上传
  const uploadProps: UploadProps = {
    name: 'file',
    action: 'http://100.90.150.110:8000/api/images/upload-simple',
    headers: {
      authorization: 'Bearer ' + localStorage.getItem('token'),
    },
    onChange(info) {
      if (info.file.status === 'uploading') {
        message.loading('图片上传中...', 0);
      } else if (info.file.status === 'done') {
        message.destroy(); // 清除loading消息
        const response = info.file.response;
        if (response?.success && response?.url) {
          const fullUrl = `http://100.90.150.110:8000${response.url}`;
          insertText(`![图片描述](${fullUrl})`);
          message.success('图片上传成功');
        } else {
          message.error('图片上传失败：' + (response?.message || '未知错误'));
        }
      } else if (info.file.status === 'error') {
        message.destroy(); // 清除loading消息
        message.error('图片上传失败');
      }
    },
    showUploadList: false,
    accept: 'image/*',
    beforeUpload: (file) => {
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('只能上传图片文件!');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('图片大小不能超过10MB!');
        return false;
      }
      return true;
    }
  };

  // 改进的Markdown到HTML转换函数 - 与前端保持一致
  const processMarkdownToHTML = (content: string): string => {
    try {
      let html = content

      // 首先保护已有的HTML标签，避免被markdown处理破坏
      const htmlTags: string[] = []
      let tagIndex = 0

      // 保护HTML标签
      html = html.replace(/<[^>]+>/g, (match) => {
        const placeholder = `__HTML_TAG_${tagIndex}__`
        htmlTags[tagIndex] = match
        tagIndex++
        return placeholder
      })

      // 处理代码块（在其他处理之前）
      html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>')
      html = html.replace(/`([^`]+)`/g, '<code>$1</code>')

      // 处理标题
      html = html.replace(/^#### (.*$)/gim, '<h4>$1</h4>')
      html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>')
      html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>')
      html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>')

      // 处理粗体和斜体
      html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      html = html.replace(/\*(.*?)\*/g, '<em>$1</em>')
      html = html.replace(/~~(.*?)~~/g, '<del>$1</del>')

      // 处理链接
      html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')

      // 处理图片
      html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" style="max-width: 100%; height: auto;" />')

      // 处理引用
      html = html.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>')

      // 处理水平线
      html = html.replace(/^---$/gm, '<hr>')

      // 处理表格
      const tableRegex = /(\|.*\|[\r\n]+\|.*\|[\r\n]+(?:\|.*\|[\r\n]*)*)/g
      html = html.replace(tableRegex, (match) => {
        const lines = match.trim().split('\n')
        if (lines.length < 2) return match

        const headers = lines[0].split('|').map(h => h.trim()).filter(h => h)
        const separator = lines[1]
        const rows = lines.slice(2).map(line =>
          line.split('|').map(cell => cell.trim()).filter(cell => cell)
        )

        let table = '<table style="border-collapse: collapse; width: 100%; margin: 1rem 0;">'
        table += '<thead><tr>'
        headers.forEach(header => {
          table += `<th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;">${header}</th>`
        })
        table += '</tr></thead><tbody>'

        rows.forEach(row => {
          table += '<tr>'
          row.forEach(cell => {
            table += `<td style="border: 1px solid #ddd; padding: 8px;">${cell}</td>`
          })
          table += '</tr>'
        })

        table += '</tbody></table>'
        return table
      })

      // 处理列表
      html = html.replace(/^(\d+)\. (.+)$/gm, '<li>$2</li>')
      html = html.replace(/^- (.+)$/gm, '<li>$1</li>')
      html = html.replace(/^(\* (.+))$/gm, '<li>$2</li>')

      // 将连续的li标签包装在ul或ol中
      html = html.replace(/(<li>.*?<\/li>[\s\n]*)+/gs, (match) => {
        return `<ul style="list-style-type: disc; padding-left: 1.5rem; margin: 1rem 0;">${match}</ul>`
      })

      // 处理段落
      const lines = html.split('\n')
      const processedLines: string[] = []
      let inParagraph = false

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim()

        // 跳过空行
        if (!line) {
          if (inParagraph) {
            processedLines.push('</p>')
            inParagraph = false
          }
          continue
        }

        // 检查是否是HTML标签行
        if (line.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|table|hr|div)/)) {
          if (inParagraph) {
            processedLines.push('</p>')
            inParagraph = false
          }
          processedLines.push(line)
        } else {
          // 普通文本行
          if (!inParagraph) {
            processedLines.push('<p style="margin: 1rem 0; line-height: 1.6;">')
            inParagraph = true
          }
          processedLines.push(line + '<br>')
        }
      }

      if (inParagraph) {
        processedLines.push('</p>')
      }

      html = processedLines.join('\n')

      // 恢复HTML标签
      for (let i = 0; i < htmlTags.length; i++) {
        html = html.replace(`__HTML_TAG_${i}__`, htmlTags[i])
      }

      // 清理多余的br标签
      html = html.replace(/<br><\/p>/g, '</p>')
      html = html.replace(/<p><\/p>/g, '')

      return html
    } catch (error) {
      console.error('Markdown processing error:', error)
      // 如果处理失败，返回原始内容并添加基本的换行处理
      return content.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')
    }
  }

  // 渲染Markdown预览
  const renderPreview = () => {
    // 检查value是否存在，避免undefined错误
    if (!value || typeof value !== 'string') {
      return <div className="text-gray-500 p-4">暂无内容预览</div>;
    }

    return <div dangerouslySetInnerHTML={{ __html: processMarkdownToHTML(value) }} style={{ lineHeight: '1.6' }} />;
  };

  return (
    <div
      className={`markdown-editor-container ${isFullscreen ? 'fullscreen' : ''}`}
      style={{
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        ...(isFullscreen && {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 1000,
          backgroundColor: 'white'
        })
      }}
    >
      {/* 工具栏 */}
      <div style={{
        padding: '8px 12px',
        borderBottom: '1px solid #d9d9d9',
        backgroundColor: '#fafafa'
      }}>
        <Space wrap>
          {/* 标题按钮 */}
          <Select
            placeholder="标题"
            style={{ width: 80 }}
            size="small"
            onChange={handleHeading}
          >
            <Option value={1}>H1</Option>
            <Option value={2}>H2</Option>
            <Option value={3}>H3</Option>
            <Option value={4}>H4</Option>
            <Option value={5}>H5</Option>
            <Option value={6}>H6</Option>
          </Select>

          <Divider type="vertical" />

          {/* 格式化按钮 */}
          <Tooltip title="粗体">
            <Button size="small" icon={<BoldOutlined />} onClick={handleBold} />
          </Tooltip>
          <Tooltip title="斜体">
            <Button size="small" icon={<ItalicOutlined />} onClick={handleItalic} />
          </Tooltip>
          <Tooltip title="行内代码">
            <Button size="small" icon={<CodeOutlined />} onClick={handleCode} />
          </Tooltip>

          <Divider type="vertical" />

          {/* 列表按钮 */}
          <Tooltip title="有序列表">
            <Button size="small" icon={<OrderedListOutlined />} onClick={handleOrderedList} />
          </Tooltip>
          <Tooltip title="无序列表">
            <Button size="small" icon={<UnorderedListOutlined />} onClick={handleUnorderedList} />
          </Tooltip>

          <Divider type="vertical" />

          {/* 插入按钮 */}
          <Tooltip title="插入链接">
            <Button
              size="small"
              icon={<LinkOutlined />}
              onClick={() => setLinkModalVisible(true)}
            />
          </Tooltip>

          <Upload {...uploadProps}>
            <Tooltip title="上传图片">
              <Button size="small" icon={<PictureOutlined />} />
            </Tooltip>
          </Upload>

          {/* 暂时注释图标按钮 */}
          {/* <Tooltip title="插入图标">
            <Button
              size="small"
              icon={<AppstoreOutlined />}
              onClick={openIconSelector}
            />
          </Tooltip> */}

          <Tooltip title="插入表格">
            <Button
              size="small"
              icon={<TableOutlined />}
              onClick={() => setTableModalVisible(true)}
            />
          </Tooltip>

          <Tooltip title="代码块">
            <Button size="small" onClick={handleCodeBlock}>
              {'</>'}
            </Button>
          </Tooltip>

          <Divider type="vertical" />

          {/* 视图控制 */}
          <Tooltip title={isFullscreen ? "退出全屏" : "全屏编辑"}>
            <Button
              size="small"
              icon={<FullscreenOutlined />}
              onClick={() => setIsFullscreen(!isFullscreen)}
            />
          </Tooltip>
        </Space>
      </div>

      {/* 编辑器内容区域 */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        style={{ height: isFullscreen ? 'calc(100vh - 57px)' : height }}
        tabBarStyle={{ margin: 0, paddingLeft: '12px' }}
        items={[
          {
            key: 'edit',
            label: '编辑',
            children: (
              <TextArea
                ref={textAreaRef}
                value={value}
                onChange={(e) => onChange(e.target.value)}
                placeholder={placeholder}
                style={{
                  border: 'none',
                  resize: 'none',
                  height: isFullscreen ? 'calc(100vh - 100px)' : height - 60
                }}
                autoSize={false}
              />
            )
          },
          {
            key: 'preview',
            label: '预览',
            children: (
              <div style={{
                padding: '12px',
                height: isFullscreen ? 'calc(100vh - 100px)' : height - 60,
                overflow: 'auto'
              }}>
                {renderPreview()}
              </div>
            )
          }
        ]}
      />

      {/* 链接插入模态框 */}
      <Modal
        title="插入链接"
        open={linkModalVisible}
        onOk={handleInsertLink}
        onCancel={() => setLinkModalVisible(false)}
        okText="插入"
        cancelText="取消"
        destroyOnHidden={true}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Input
            placeholder="链接文本"
            value={linkText}
            onChange={(e) => setLinkText(e.target.value)}
          />
          <Input
            placeholder="链接地址"
            value={linkUrl}
            onChange={(e) => setLinkUrl(e.target.value)}
          />
        </Space>
      </Modal>

      {/* 表格插入模态框 */}
      <Modal
        title="插入表格"
        open={tableModalVisible}
        onOk={handleInsertTable}
        onCancel={() => setTableModalVisible(false)}
        okText="插入"
        cancelText="取消"
        destroyOnHidden={true}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <span>行数：</span>
            <Select
              value={tableRows}
              onChange={setTableRows}
              style={{ width: 80, marginLeft: 8 }}
            >
              {[1,2,3,4,5,6,7,8,9,10].map(n => (
                <Option key={n} value={n}>{n}</Option>
              ))}
            </Select>
          </div>
          <div>
            <span>列数：</span>
            <Select
              value={tableCols}
              onChange={setTableCols}
              style={{ width: 80, marginLeft: 8 }}
            >
              {[1,2,3,4,5,6,7,8,9,10].map(n => (
                <Option key={n} value={n}>{n}</Option>
              ))}
            </Select>
          </div>
        </Space>
      </Modal>

      {/* 图标选择器 (暂时注释) */}
      {/* <IconSelectorComponent /> */}
    </div>
  );
};

export default MarkdownEditor;