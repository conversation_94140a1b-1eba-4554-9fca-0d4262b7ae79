import { getBlogBySlug } from './blogs'
import { getProjectBySlug } from './projects'
import { processMarkdownFast } from './markdown-processor'
import { getMDXContentOptimized } from './mdx-optimized'

// 优化的Markdown到HTML转换函数
function processMarkdown(content: string): string {
  try {
    // 如果内容为空或过短，直接返回
    if (!content || content.length < 10) {
      return content || ''
    }

    let html = content

    // 首先保护已有的HTML标签，避免被markdown处理破坏
    const htmlTags: string[] = []
    let tagIndex = 0

    // 保护HTML标签 - 优化正则表达式
    html = html.replace(/<[^>]+>/g, (match) => {
      const placeholder = `__HTML_TAG_${tagIndex}__`
      htmlTags[tagIndex] = match
      tagIndex++
      return placeholder
    })

    // 处理代码块（在其他处理之前）
    html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
      const language = lang || 'text'
      const cleanCode = code.trim()
      // 使用特殊标记，稍后在前端组件中处理
      return `<div data-code-block="true" data-language="${language}">${cleanCode}</div>`
    })
    html = html.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>')

    // 处理标题
    html = html.replace(/^#### (.*$)/gim, '<h4>$1</h4>')
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>')
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>')
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>')

    // 处理粗体和斜体
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>')
    html = html.replace(/~~(.*?)~~/g, '<del>$1</del>')

    // 处理链接
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')

    // 处理图标（特殊的图片语法，URL包含/api/icons/render/）
    html = html.replace(/!\[([^\]]*)\]\((\/api\/icons\/render\/[^)]+)\)/g, '<span data-icon-placeholder data-src="$2" data-alt="$1"></span>')

    // 处理普通图片
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" style="max-width: 100%; height: auto;" />')

    // 处理引用
    html = html.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>')

    // 处理水平线
    html = html.replace(/^---$/gm, '<hr>')

    // 处理表格
    const tableRegex = /(\|.*\|[\r\n]+\|.*\|[\r\n]+(?:\|.*\|[\r\n]*)*)/g
    html = html.replace(tableRegex, (match) => {
      const lines = match.trim().split('\n')
      if (lines.length < 2) return match

      const headers = lines[0].split('|').map(h => h.trim()).filter(h => h)
      const separator = lines[1]
      const rows = lines.slice(2).map(line =>
        line.split('|').map(cell => cell.trim()).filter(cell => cell)
      )

      let table = '<table class="table-auto border-collapse border border-gray-300 w-full">'
      table += '<thead><tr>'
      headers.forEach(header => {
        table += `<th class="border border-gray-300 px-4 py-2 bg-gray-100">${header}</th>`
      })
      table += '</tr></thead><tbody>'

      rows.forEach(row => {
        table += '<tr>'
        row.forEach(cell => {
          table += `<td class="border border-gray-300 px-4 py-2">${cell}</td>`
        })
        table += '</tr>'
      })

      table += '</tbody></table>'
      return table
    })

    // 处理列表
    html = html.replace(/^(\d+)\. (.+)$/gm, '<li>$2</li>')
    html = html.replace(/^- (.+)$/gm, '<li>$1</li>')
    html = html.replace(/^(\* (.+))$/gm, '<li>$2</li>')

    // 将连续的li标签包装在ul或ol中
    html = html.replace(/(<li>.*?<\/li>[\s\n]*)+/gs, (match) => {
      return `<ul class="list-disc list-inside space-y-1">${match}</ul>`
    })

    // 处理段落
    const lines = html.split('\n')
    const processedLines: string[] = []
    let inParagraph = false

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()

      // 跳过空行
      if (!line) {
        if (inParagraph) {
          processedLines.push('</p>')
          inParagraph = false
        }
        continue
      }

      // 检查是否是HTML标签行
      if (line.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|table|hr|div)/)) {
        if (inParagraph) {
          processedLines.push('</p>')
          inParagraph = false
        }
        processedLines.push(line)
      } else {
        // 普通文本行
        if (!inParagraph) {
          processedLines.push('<p>')
          inParagraph = true
        }
        processedLines.push(line + '<br>')
      }
    }

    if (inParagraph) {
      processedLines.push('</p>')
    }

    html = processedLines.join('\n')

    // 恢复HTML标签
    for (let i = 0; i < htmlTags.length; i++) {
      html = html.replace(`__HTML_TAG_${i}__`, htmlTags[i])
    }

    // 清理多余的br标签
    html = html.replace(/<br><\/p>/g, '</p>')
    html = html.replace(/<p><\/p>/g, '')

    return html
  } catch (error) {
    console.error('Markdown processing error:', error)
    // 如果处理失败，返回原始内容并添加基本的换行处理
    return content.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')
  }
}

export async function getMDXContent(slug: string, contentType?: 'blog' | 'project', existingContent?: string) {
  let content: string | undefined = existingContent;

  try {
    // 如果已经提供了内容，直接使用，避免重复API调用
    if (!content) {
      if (contentType === 'project') {
        // 获取项目内容
        const project = await getProjectBySlug(slug);
        content = project?.content;
      } else {
        // 默认尝试获取博客内容
        const blog = await getBlogBySlug(slug);
        content = blog?.content;

        // 如果博客获取失败，尝试获取项目内容
        if (!content) {
          
          const project = await getProjectBySlug(slug);
          content = project?.content;
        }
      }
    }

    if (!content) {
      throw new Error(`Failed to load content for slug: ${slug}`);
    }

    // 使用优化的缓存 MDX 处理器
    const result = await getMDXContentOptimized(content, slug, {
      includeStats: process.env.NODE_ENV === 'development'
    });
    
    // 在开发环境下记录性能统计（仅在显式启用时）
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_MDX_DEBUG === 'true' && result.stats) {
      // console.log(`📊 MDX Stats for ${slug}:`, { // 已清理调试代码
        processingTime: `${result.stats.processingTime.toFixed(2)}ms`,
        cacheHit: result.stats.cacheHit ? '✅ Cache Hit' : '❌ Cache Miss',
        contentSize: `${(result.stats.contentLength / 1024).toFixed(2)}KB`
      });
    }
    
    return result.content;

  } catch (error) {
    console.error(`获取内容失败 (slug: ${slug}, type: ${contentType}):`, error);
    throw new Error(`Failed to load content for slug: ${slug}`);
  }
}