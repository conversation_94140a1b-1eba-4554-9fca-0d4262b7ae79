interface WalineConfig {
  serverURL: string;
  lang: string;
}

interface WalineConfigResponse {
  server_url: string;
  lang: string;
}

// 增强的缓存配置管理
let cachedConfig: WalineConfig | null = null;
let configPromise: Promise<WalineConfig> | null = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1秒基础延迟

/**
 * 延迟函数
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 带重试机制的Waline配置获取
 */
async function fetchWalineConfigWithRetry(retryCount = 0): Promise<WalineConfig> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

    const response = await fetch('/api/system-config/waline/config', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: controller.signal,
      // 添加缓存策略
      cache: 'default',
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data: WalineConfigResponse = await response.json();
    
    const config: WalineConfig = {
      serverURL: data.server_url,
      lang: data.lang || 'zh-CN',
    };

    // 验证配置有效性
    if (!config.serverURL || config.serverURL.trim() === '') {
      throw new Error('Invalid server URL received');
    }

    return config;
    
  } catch (error) {
    // 如果还有重试次数，进行重试
    if (retryCount < MAX_RETRIES) {
      const delayMs = RETRY_DELAY * Math.pow(2, retryCount); // 指数退避
      
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_WALINE_DEBUG === 'true') {
        console.warn(`Waline配置获取失败，${delayMs}ms后重试 (${retryCount + 1}/${MAX_RETRIES}):`, error);
      }
      
      await delay(delayMs);
      return fetchWalineConfigWithRetry(retryCount + 1);
    }

    // 所有重试都失败了，记录错误并返回默认配置
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_WALINE_DEBUG === 'true') {
      console.error('获取Waline配置彻底失败，使用默认配置:', error);
    }
    
    // 返回默认配置作为后备
    return {
      serverURL: 'https://waline.jyaochen.cn',
      lang: 'zh-CN',
    };
  }
}

/**
 * 从后台API获取Waline配置（兼容老版本）
 */
async function fetchWalineConfig(): Promise<WalineConfig> {
  return fetchWalineConfigWithRetry();
}

/**
 * 获取Waline配置（带缓存）
 */
export async function getWalineConfig(): Promise<WalineConfig> {
  const now = Date.now();
  
  // 检查缓存是否还有效
  if (cachedConfig && (now - lastFetchTime) < CACHE_DURATION) {
    return cachedConfig;
  }

  // 如果正在请求中，等待请求完成
  if (configPromise) {
    return configPromise;
  }

  // 发起新的请求
  configPromise = fetchWalineConfigWithRetry();

  try {
    const config = await configPromise;
    cachedConfig = config;
    lastFetchTime = now;
    
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_WALINE_DEBUG === 'true') {
      // console.log('Waline配置获取成功:', config); // 已清理调试代码
    }
    
    return config;
  } catch (error) {
    // 请求失败，但不清除旧缓存（如果有的话）
    if (cachedConfig) {
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_WALINE_DEBUG === 'true') {
        console.warn('配置获取失败，使用缓存的配置:', cachedConfig);
      }
      return cachedConfig;
    }
    
    // 没有缓存配置，抛出错误
    throw error;
  } finally {
    // 请求完成，清除promise缓存
    configPromise = null;
  }
}

/**
 * 清除配置缓存（用于配置更新后刷新）
 */
export function clearWalineConfigCache(): void {
  cachedConfig = null;
  configPromise = null;
  lastFetchTime = 0;
  
  if (process.env.NODE_ENV === 'development' && process.env.ENABLE_WALINE_DEBUG === 'true') {
    // console.log('Waline配置缓存已清除'); // 已清理调试代码
  }
}

/**
 * 预加载Waline配置
 * 在用户可能需要评论功能之前预先获取配置
 */
export async function preloadWalineConfig(): Promise<void> {
  try {
    await getWalineConfig();
  } catch (error) {
    // 预加载失败不影响主流程
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_WALINE_DEBUG === 'true') {
      console.warn('Waline配置预加载失败:', error);
    }
  }
}

/**
 * 获取配置缓存状态
 */
export function getWalineConfigCacheStatus(): {
  isCached: boolean;
  isExpired: boolean;
  lastFetchTime: number;
  cacheAge: number;
} {
  const now = Date.now();
  const cacheAge = now - lastFetchTime;
  
  return {
    isCached: !!cachedConfig,
    isExpired: cacheAge > CACHE_DURATION,
    lastFetchTime,
    cacheAge
  };
}

/**
 * 获取Waline服务器URL
 */
export async function getWalineServerURL(): Promise<string> {
  const config = await getWalineConfig();
  return config.serverURL;
}

/**
 * 获取Waline语言设置
 */
export async function getWalineLang(): Promise<string> {
  const config = await getWalineConfig();
  return config.lang;
}
