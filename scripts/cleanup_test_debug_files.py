#!/usr/bin/env python3
"""
清理测试和调试文件脚本
专门用于清理项目中的测试文件、调试文件和日志文件
"""

import os
import shutil
from pathlib import Path
import argparse

class TestDebugCleaner:
    def __init__(self, project_root=None):
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.removed_files = []
        self.removed_dirs = []
        self.total_size_saved = 0
        
    def get_file_size(self, path):
        """获取文件或目录大小"""
        if path.is_file():
            return path.stat().st_size
        elif path.is_dir():
            total = 0
            try:
                for item in path.rglob('*'):
                    if item.is_file():
                        total += item.stat().st_size
            except (PermissionError, OSError):
                pass
            return total
        return 0
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f}{size_names[i]}"
    
    def remove_file_or_dir(self, path, description="", dry_run=False):
        """删除文件或目录"""
        if not path.exists():
            return
            
        size = self.get_file_size(path)
        size_str = self.format_size(size)
        
        if dry_run:
            if path.is_dir():
                print(f"  📁 {path} ({size_str}) - {description}")
            else:
                print(f"  📄 {path} ({size_str}) - {description}")
            return
        
        try:
            if path.is_dir():
                shutil.rmtree(path)
                self.removed_dirs.append(str(path))
                print(f"✅ 删除目录: {path} ({size_str}) - {description}")
            else:
                path.unlink()
                self.removed_files.append(str(path))
                print(f"✅ 删除文件: {path} ({size_str}) - {description}")
            
            self.total_size_saved += size
            
        except Exception as e:
            print(f"❌ 删除失败 {path}: {str(e)}")
    
    def clean_test_files(self, dry_run=False):
        """清理测试文件"""
        print("\n🧪 清理测试文件...")
        
        # 测试目录
        test_dirs = [
            "frontend/src/app/test-waline",
            "frontend/src/app/performance-test", 
            "frontend/src/app/test-emoji",
        ]
        
        for dir_path in test_dirs:
            full_path = self.project_root / dir_path
            self.remove_file_or_dir(full_path, "测试目录", dry_run)
        
        # 测试文件
        test_files = [
            "frontend/src/lib/waline-performance-test.ts",
            "frontend/src/lib/performance-test.ts",
        ]
        
        for file_path in test_files:
            full_path = self.project_root / file_path
            self.remove_file_or_dir(full_path, "测试文件", dry_run)
    
    def clean_debug_files(self, dry_run=False):
        """清理调试文件"""
        print("\n🐛 清理调试文件...")
        
        # 调试目录
        debug_dirs = [
            "admin/src/components/debug",
        ]
        
        for dir_path in debug_dirs:
            full_path = self.project_root / dir_path
            self.remove_file_or_dir(full_path, "调试组件目录", dry_run)
    
    def clean_log_files(self, dry_run=False):
        """清理日志文件"""
        print("\n📋 清理日志文件...")
        
        log_files = [
            "backend/backend.log",
            "backend/backend_new.log", 
            "backend/migrations.log",
            "frontend/frontend-dev.log",
            "frontend/frontend.log",
        ]
        
        for file_path in log_files:
            full_path = self.project_root / file_path
            self.remove_file_or_dir(full_path, "日志文件", dry_run)
    
    def clean_temp_files(self, dry_run=False):
        """清理临时文件"""
        print("\n🗂️  清理临时文件...")
        
        # 查找.DS_Store文件
        for ds_store in self.project_root.rglob(".DS_Store"):
            self.remove_file_or_dir(ds_store, "macOS系统文件", dry_run)
        
        # 查找临时目录
        temp_dirs = [
            "admin/node_modules/.tmp",
        ]
        
        for dir_path in temp_dirs:
            full_path = self.project_root / dir_path
            self.remove_file_or_dir(full_path, "临时目录", dry_run)
    
    def clean_build_artifacts(self, dry_run=False):
        """清理构建产物"""
        print("\n🔨 清理构建产物...")
        
        # Next.js构建缓存中的调试文件
        debug_files = list(self.project_root.glob("frontend/.next/server/vendor-chunks/debug.js"))
        for debug_file in debug_files:
            self.remove_file_or_dir(debug_file, "构建调试文件", dry_run)
    
    def print_summary(self):
        """打印清理总结"""
        print("\n" + "="*60)
        print("🎉 清理完成!")
        print(f"📁 删除目录: {len(self.removed_dirs)}个")
        print(f"📄 删除文件: {len(self.removed_files)}个") 
        print(f"💾 节省空间: {self.format_size(self.total_size_saved)}")
        print("="*60)
    
    def run_cleanup(self, dry_run=False):
        """运行清理"""
        if dry_run:
            print("🔍 DRY RUN 模式 - 仅显示将要删除的文件")
        else:
            print("🧹 开始清理测试和调试文件...")
        
        print(f"项目根目录: {self.project_root}")
        
        self.clean_test_files(dry_run)
        self.clean_debug_files(dry_run)
        self.clean_log_files(dry_run)
        self.clean_temp_files(dry_run)
        self.clean_build_artifacts(dry_run)
        
        if not dry_run:
            self.print_summary()

def main():
    parser = argparse.ArgumentParser(description="清理测试和调试文件")
    parser.add_argument("--dry-run", action="store_true",
                       help="仅显示将要删除的文件，不实际删除")
    parser.add_argument("--project-root", type=str,
                       help="指定项目根目录")
    
    args = parser.parse_args()
    
    cleaner = TestDebugCleaner(args.project_root)
    
    if not args.dry_run:
        confirm = input("确定要清理这些测试和调试文件吗？(y/N): ")
        if confirm.lower() != 'y':
            print("❌ 清理已取消")
            return
    
    cleaner.run_cleanup(args.dry_run)

if __name__ == "__main__":
    main()
