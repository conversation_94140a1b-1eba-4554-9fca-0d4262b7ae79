#!/usr/bin/env python3
"""
清理console调试代码脚本
专门用于清理项目中的console.log、console.debug等调试代码
保留console.error和console.warn等重要日志
"""

import os
import re
from pathlib import Path
import argparse

class ConsoleCleaner:
    def __init__(self, project_root=None):
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.cleaned_files = []
        self.total_lines_removed = 0
        
        # 需要清理的console方法
        self.debug_methods = [
            'console.log',
            'console.debug', 
            'console.info',
            'console.trace',
            'console.time',
            'console.timeEnd',
            'console.count',
            'console.group',
            'console.groupEnd',
            'console.table'
        ]
        
        # 保留的console方法（错误和警告）
        self.keep_methods = [
            'console.error',
            'console.warn'
        ]
        
        # 需要处理的文件扩展名
        self.file_extensions = ['.ts', '.tsx', '.js', '.jsx']
        
        # 排除的目录
        self.exclude_dirs = [
            'node_modules',
            '.venv', 
            '.git',
            '.next',
            'dist',
            'build',
            '__pycache__'
        ]
    
    def should_process_file(self, file_path):
        """判断是否应该处理该文件"""
        # 检查文件扩展名
        if file_path.suffix not in self.file_extensions:
            return False
            
        # 检查是否在排除目录中
        for exclude_dir in self.exclude_dirs:
            if exclude_dir in file_path.parts:
                return False
                
        return True
    
    def clean_console_logs(self, content):
        """清理文件中的console调试代码"""
        lines = content.split('\n')
        cleaned_lines = []
        removed_count = 0
        
        for line in lines:
            should_remove = False
            
            # 检查是否包含需要清理的console方法
            for method in self.debug_methods:
                if method in line:
                    # 确保不是注释中的console
                    stripped = line.strip()
                    if not (stripped.startswith('//') or stripped.startswith('*') or stripped.startswith('/*')):
                        should_remove = True
                        break
            
            if should_remove:
                removed_count += 1
                # 保留缩进，但注释掉该行
                indent = len(line) - len(line.lstrip())
                cleaned_lines.append(' ' * indent + '// ' + line.strip() + ' // 已清理调试代码')
            else:
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines), removed_count
    
    def process_file(self, file_path, dry_run=False):
        """处理单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            cleaned_content, removed_count = self.clean_console_logs(original_content)
            
            if removed_count > 0:
                if dry_run:
                    print(f"  📄 {file_path} - 将清理 {removed_count} 行调试代码")
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(cleaned_content)
                    
                    self.cleaned_files.append(str(file_path))
                    self.total_lines_removed += removed_count
                    print(f"✅ 清理文件: {file_path} - 清理了 {removed_count} 行调试代码")
                    
        except Exception as e:
            print(f"❌ 处理文件失败 {file_path}: {str(e)}")
    
    def find_and_clean_files(self, dry_run=False):
        """查找并清理所有文件"""
        if dry_run:
            print("🔍 DRY RUN 模式 - 仅显示将要清理的文件")
        else:
            print("🧹 开始清理console调试代码...")
        
        print(f"项目根目录: {self.project_root}")
        print(f"清理的console方法: {', '.join(self.debug_methods)}")
        print(f"保留的console方法: {', '.join(self.keep_methods)}")
        print()
        
        # 遍历所有文件
        for file_path in self.project_root.rglob('*'):
            if file_path.is_file() and self.should_process_file(file_path):
                self.process_file(file_path, dry_run)
    
    def print_summary(self):
        """打印清理总结"""
        print("\n" + "="*60)
        print("🎉 Console调试代码清理完成!")
        print(f"📄 处理文件: {len(self.cleaned_files)}个")
        print(f"🗑️  清理行数: {self.total_lines_removed}行")
        print("="*60)
        
        if self.cleaned_files:
            print("\n清理的文件列表:")
            for file_path in self.cleaned_files:
                print(f"  - {file_path}")
    
    def run_cleanup(self, dry_run=False):
        """运行清理"""
        self.find_and_clean_files(dry_run)
        
        if not dry_run:
            self.print_summary()

def main():
    parser = argparse.ArgumentParser(description="清理console调试代码")
    parser.add_argument("--dry-run", action="store_true",
                       help="仅显示将要清理的文件，不实际清理")
    parser.add_argument("--project-root", type=str,
                       help="指定项目根目录")
    
    args = parser.parse_args()
    
    cleaner = ConsoleCleaner(args.project_root)
    
    if not args.dry_run:
        print("⚠️  注意：这将清理所有console.log等调试代码（保留console.error和console.warn）")
        confirm = input("确定要继续吗？(y/N): ")
        if confirm.lower() != 'y':
            print("❌ 清理已取消")
            return
    
    cleaner.run_cleanup(args.dry_run)

if __name__ == "__main__":
    main()
